# 邮箱按钮功能添加总结

## 功能概述

在关于按钮旁边新增了一个"邮箱"按钮，点击后打开QQ邮箱验证码监控对话框，提供自动监控QQ邮箱验证码邮件的功能。

## 修改的文件

### 1. gui_qt6/main_page.py
**主要修改：**
- 在顶部布局中添加了邮箱按钮
- 添加了邮箱按钮的信号连接
- 在`_update_ui_texts`方法中添加了邮箱按钮的文本更新
- 实现了`_show_email`方法来显示邮箱监控对话框

**具体变更：**
```python
# 邮箱按钮
self.email_btn = ModernButton(get_text("app.email"), "secondary")
# 根据语言调整按钮宽度
if self.config_manager.get_language() == "en_US":
    self.email_btn.setMaximumWidth(100)
else:
    self.email_btn.setMaximumWidth(80)
top_layout.addWidget(self.email_btn)

# 信号连接
self.email_btn.clicked.connect(self._show_email)

# 显示邮箱对话框方法
def _show_email(self):
    """显示邮箱监控对话框"""
    from .email_dialog import EmailDialog
    EmailDialog(self, self.config_manager).show()
```

### 2. gui_qt6/email_dialog.py (新建文件)
**完整的邮箱监控对话框实现：**

#### EmailMonitorWorker类 (工作线程)
- 使用IMAP协议连接QQ邮箱
- 后台监控新邮件
- 识别包含验证码关键词的邮件
- 通过PyQt6信号与界面通信

#### EmailDialog类 (对话框界面)
- 现代化的PyQt6界面设计
- 开始/停止监控按钮
- 实时状态显示
- 监控日志区域
- 完善的错误处理

#### 邮箱配置 (EMAIL_CONFIG)
```python
EMAIL_CONFIG = {
    'imap_server': 'imap.qq.com',
    'imap_port': 993,
    'email': '<EMAIL>',  # 需要配置
    'password': 'your_app_password',   # 需要配置
    'check_interval': 10,  # 检查间隔（秒）
    'keywords': ['验证码', '验证', 'verification', 'code']
}
```

### 3. languages/zh_CN.json
**添加内容：**
```json
"app": {
  "email": "邮箱",
  // ... 其他内容 ...
},
"dialogs": {
  "titles": {
    "email_title": "QQ邮箱验证码监控",
    // ... 其他内容 ...
  }
}
```

### 4. languages/en_US.json
**添加内容：**
```json
"app": {
  "email": "Email",
  // ... 其他内容 ...
},
"dialogs": {
  "titles": {
    "email_title": "QQ Email Verification Code Monitor",
    // ... 其他内容 ...
  }
}
```

## 功能特性

### 界面设计
- **按钮位置**: 位于关于按钮左侧，保持界面平衡
- **按钮样式**: 使用secondary样式，与关于按钮保持一致
- **多语言支持**: 中文显示"邮箱"，英文显示"Email"
- **响应式宽度**: 根据语言自动调整按钮宽度

### 邮箱监控功能
- **IMAP连接**: 使用SSL加密连接QQ邮箱
- **实时监控**: 每10秒检查一次新邮件
- **智能识别**: 自动识别包含验证码关键词的邮件
- **多线程处理**: 后台监控不阻塞界面操作

### 验证码识别
支持的关键词：
- 中文：验证码、验证
- 英文：verification、code

识别范围：
- 邮件主题
- 邮件正文内容

### 用户界面
- **现代化设计**: 使用#4EA5FF主色调，与整体风格一致
- **状态反馈**: 实时显示连接状态和监控状态
- **日志显示**: 详细记录监控过程和收到的验证码邮件
- **操作简单**: 一键开始/停止监控

### 安全特性
- **配置隐藏**: 邮箱配置在代码中，不暴露在界面上
- **SSL加密**: 所有邮箱通信都经过SSL加密
- **本地处理**: 所有数据处理都在本地进行
- **授权码认证**: 使用QQ邮箱授权码而非密码

## 技术实现

### 架构设计
```
主界面 (main_page.py)
    ↓ 点击邮箱按钮
邮箱对话框 (email_dialog.py)
    ↓ 启动监控
邮箱监控线程 (EmailMonitorWorker)
    ↓ IMAP连接
QQ邮箱服务器 (imap.qq.com:993)
```

### 核心技术栈
- **PyQt6**: 界面框架和信号机制
- **imaplib**: Python标准库IMAP客户端
- **email**: 邮件解析库
- **threading**: 多线程支持
- **SSL**: 安全连接

### 监控流程
1. **连接建立**: 连接到imap.qq.com:993
2. **身份验证**: 使用邮箱地址和授权码登录
3. **邮箱选择**: 选择收件箱(inbox)
4. **基准获取**: 获取当前邮件数量作为监控基准
5. **循环监控**: 每10秒检查一次新邮件
6. **内容分析**: 解析新邮件的主题和内容
7. **关键词匹配**: 检查是否包含验证码关键词
8. **结果显示**: 在界面中显示匹配的验证码邮件

### 错误处理
- **连接失败**: 显示具体错误信息，支持重试
- **认证失败**: 提示检查邮箱配置
- **网络中断**: 自动重连机制
- **邮件解析错误**: 跳过有问题的邮件，继续监控

## 使用流程

### 1. 配置准备
1. 开启QQ邮箱IMAP服务
2. 获取QQ邮箱授权码
3. 在代码中配置邮箱信息

### 2. 启动监控
1. 点击主界面右上角的"邮箱"按钮
2. 在弹出的对话框中点击"开始监控"
3. 查看状态显示确认连接成功

### 3. 监控验证码
1. 系统自动监控新邮件
2. 识别到验证码邮件时会在日志中显示
3. 显示发件人、主题、内容和时间信息

### 4. 停止监控
1. 点击"停止监控"按钮
2. 或直接关闭对话框

## 兼容性保证

### 不影响原有功能
- ✅ 所有原有按钮功能保持不变
- ✅ 界面布局自适应调整
- ✅ 多语言切换功能正常
- ✅ 其他所有功能保持原样

### 界面兼容性
- ✅ 按钮样式与现有设计风格一致
- ✅ 布局自适应，不影响其他元素
- ✅ 多语言界面正常显示
- ✅ 响应式设计，适配不同窗口大小

## 配置要求

### 必需配置
1. **QQ邮箱地址**: 替换EMAIL_CONFIG中的email字段
2. **QQ邮箱授权码**: 替换EMAIL_CONFIG中的password字段

### 可选配置
1. **检查间隔**: 调整check_interval值（默认10秒）
2. **关键词列表**: 添加更多验证码识别关键词
3. **IMAP设置**: 如需支持其他邮箱服务

## 安全注意事项

1. **授权码保护**: 授权码等同于邮箱密码，请妥善保管
2. **本地存储**: 配置信息仅存储在本地代码中
3. **加密传输**: 使用SSL加密连接，确保通信安全
4. **权限最小化**: 仅读取邮箱内容，不进行任何修改操作

## 扩展可能

1. **支持更多邮箱**: 可扩展支持Gmail、Outlook等
2. **高级过滤**: 可添加更复杂的邮件过滤规则
3. **通知功能**: 可添加系统通知或声音提醒
4. **历史记录**: 可保存验证码历史记录
5. **自动复制**: 可自动提取并复制验证码到剪贴板

## 测试验证

功能已通过以下测试：
- ✅ 界面正常显示邮箱按钮
- ✅ 按钮点击响应正常
- ✅ 对话框正常打开和关闭
- ✅ 多语言文本显示正确
- ✅ 不影响原有功能
- ✅ 错误处理机制正常

功能已成功集成到现有系统中，可以正常使用。用户只需按照配置指南设置邮箱信息即可开始使用验证码监控功能。
