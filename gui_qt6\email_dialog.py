#!/usr/bin/env python3
"""
PyQt6邮箱监控对话框
用于QQ邮箱验证码监控功能
"""

import imaplib
import email
import time
import threading
import re
import pyperclip
import subprocess
import os
from email.header import decode_header
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.action_chains import ActionChains
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTextEdit, QFrame, QProgressBar,
    QInputDialog, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QFont

from .font_manager import get_default_font, get_title_font, get_monospace_font
from language_manager import get_text


def get_target_email_from_user(parent=None):
    """获取用户输入的目标邮箱地址"""
    try:
        dialog = QInputDialog(parent)
        dialog.setWindowTitle("输入收信人邮箱")
        dialog.setLabelText("请输入要监控的收信人邮箱地址：\n(例如：<EMAIL>)")
        dialog.setTextValue("")
        dialog.resize(400, 150)

        # 设置样式
        dialog.setStyleSheet("""
            QInputDialog {
                background-color: #f8fafc;
            }
            QLabel {
                color: #374151;
                font-size: 12px;
                margin: 10px;
            }
            QLineEdit {
                border: 2px solid #d1d5db;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
            }
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)

        if dialog.exec() == QInputDialog.DialogCode.Accepted:
            email = dialog.textValue().strip()
            if email and '@' in email:
                return email
            else:
                QMessageBox.warning(parent, "输入错误", "请输入有效的邮箱地址！")
                return None
        else:
            return None
    except Exception as e:
        QMessageBox.critical(parent, "错误", f"获取邮箱地址失败: {str(e)}")
        return None


# 邮箱配置 - 在代码中配置，不暴露在界面上
EMAIL_CONFIG = {
    'imap_server': 'imap.qq.com',
    'imap_port': 993,
    'email': '<EMAIL>',  # 请替换为实际的QQ邮箱
    'password': 'earnbaqecvgojeef',   # 请替换为QQ邮箱的授权码
    'check_interval': 10,  # 检查间隔（秒）
    'keywords': ['验证码', '验证', 'Augment Code', 'code']  # 验证码关键词
}


class EmailMonitorWorker(QThread):
    """邮箱监控工作线程"""

    # 信号定义
    message_received = pyqtSignal(str)  # 收到新邮件
    status_changed = pyqtSignal(str)    # 状态变化
    error_occurred = pyqtSignal(str)    # 发生错误
    verification_code_found = pyqtSignal(str, str, str)  # 验证码, 发件人, 主题
    countdown_update = pyqtSignal(int)  # 倒计时更新

    def __init__(self, target_email=None):
        super().__init__()
        self.running = False
        self.mail = None
        self.target_email = target_email  # 目标收信人邮箱

    def run(self):
        """运行邮箱监控"""
        self.running = True
        self.status_changed.emit("正在连接邮箱服务器...")

        try:
            # 连接到IMAP服务器
            self.mail = imaplib.IMAP4_SSL(EMAIL_CONFIG['imap_server'], EMAIL_CONFIG['imap_port'])
            self.mail.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
            self.mail.select('inbox')

            self.status_changed.emit("邮箱连接成功，开始监控...")

            # 获取初始邮件数量
            last_count = len(self.mail.search(None, 'ALL')[1][0].split())

            while self.running:
                try:
                    # 检查新邮件
                    current_count = len(self.mail.search(None, 'ALL')[1][0].split())

                    if current_count > last_count:
                        # 有新邮件，检查是否包含验证码
                        self._check_new_emails(last_count + 1, current_count)
                        last_count = current_count

                    # 等待下次检查
                    for _ in range(EMAIL_CONFIG['check_interval']):
                        if not self.running:
                            break
                        time.sleep(1)

                except Exception as e:
                    self.error_occurred.emit(f"监控过程中发生错误: {str(e)}")
                    time.sleep(5)  # 错误后等待5秒再重试

        except Exception as e:
            self.error_occurred.emit(f"邮箱连接失败: {str(e)}")
        finally:
            if self.mail:
                try:
                    self.mail.close()
                    self.mail.logout()
                except:
                    pass

    def _check_new_emails(self, start_index, end_index):
        """检查新邮件是否包含验证码"""
        try:
            for i in range(start_index, end_index + 1):
                # 获取邮件
                typ, msg_data = self.mail.fetch(str(i), '(RFC822)')
                if typ == 'OK':
                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)

                    # 解析邮件主题
                    subject = self._decode_header(email_message['Subject'])
                    sender = self._decode_header(email_message['From'])

                    # 获取邮件内容
                    content = self._get_email_content(email_message)

                    # 如果指定了目标邮箱，检查是否是发给目标邮箱的邮件
                    if self.target_email:
                        if not self._is_email_for_target(email_message, content):
                            continue  # 跳过不是发给目标邮箱的邮件

                    # 检查是否包含验证码关键词
                    if any(keyword in subject.lower() or keyword in content.lower()
                           for keyword in EMAIL_CONFIG['keywords']):

                        # 提取验证码
                        verification_code = self._extract_verification_code(subject + " " + content)

                        if verification_code:
                            # 发送验证码信号
                            self.verification_code_found.emit(verification_code, sender, subject)

                            # 优化的日志格式
                            timestamp = time.strftime("%H:%M:%S")
                            if self.target_email:
                                self.message_received.emit(f"📧 收到来自 {self.target_email} 的验证码邮件")
                            else:
                                self.message_received.emit(f"📧 收到验证码邮件")
                            self.message_received.emit(f"✅ 验证码: {verification_code}")
                            self.message_received.emit(f"📤 发件人: {sender}")
                            self.message_received.emit(f"📋 主题: {subject}")
                        else:
                            # 没有提取到验证码，显示原始信息
                            timestamp = time.strftime("%H:%M:%S")
                            self.message_received.emit(f"📧 收到可能的验证码邮件")
                            self.message_received.emit(f"📤 发件人: {sender}")
                            self.message_received.emit(f"📋 主题: {subject}")
                            self.message_received.emit(f"📄 内容: {content[:100]}...")

        except Exception as e:
            self.error_occurred.emit(f"检查邮件时发生错误: {str(e)}")

    def _is_email_for_target(self, email_message, content):
        """检查邮件是否是发给目标邮箱的"""
        try:
            if not self.target_email:
                return True  # 如果没有指定目标邮箱，接受所有邮件

            target_email_lower = self.target_email.lower()

            # 检查邮件头部字段
            headers_to_check = ['To', 'Delivered-To', 'X-Original-To', 'X-Envelope-To', 'Envelope-To']
            for header_name in headers_to_check:
                header_value = email_message.get(header_name)
                if header_value:
                    decoded_header = self._decode_header(header_value)
                    if target_email_lower in decoded_header.lower():
                        return True

            # 检查邮件内容中是否包含目标邮箱
            if target_email_lower in content.lower():
                return True

            # 检查邮件主题中是否包含目标邮箱
            subject = self._decode_header(email_message['Subject'])
            if subject and target_email_lower in subject.lower():
                return True

            return False

        except Exception as e:
            # 如果检查过程中出错，默认接受邮件
            return True

    def _decode_header(self, header):
        """解码邮件头"""
        if header is None:
            return ""

        decoded_header = decode_header(header)
        header_str = ""

        for part, encoding in decoded_header:
            if isinstance(part, bytes):
                if encoding:
                    header_str += part.decode(encoding)
                else:
                    header_str += part.decode('utf-8', errors='ignore')
            else:
                header_str += part

        return header_str

    def _get_email_content(self, email_message):
        """获取邮件内容"""
        content = ""

        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    payload = part.get_payload(decode=True)
                    if payload:
                        content += payload.decode('utf-8', errors='ignore')
        else:
            payload = email_message.get_payload(decode=True)
            if payload:
                content = payload.decode('utf-8', errors='ignore')

        return content

    def _extract_verification_code(self, text):
        """从文本中提取验证码 - 参考VIP项目的实现"""
        # 优先级排序的验证码格式正则表达式
        patterns = [
            (r'验证码[：:\s]*[是为]?\s*(\d{4,8})', "中文验证码格式1"),
            (r'验证码[：:\s]*(\d{4,8})', "中文验证码格式2"),
            (r'verification\s*code[：:\s]*(\d{4,8})', "英文验证码格式1"),
            (r'code[：:\s]*(\d{4,8})', "英文验证码格式2"),
            (r'动态码[：:\s]*(\d{4,8})', "动态码格式"),
            (r'安全码[：:\s]*(\d{4,8})', "安全码格式"),
            (r'校验码[：:\s]*(\d{4,8})', "校验码格式"),
            (r'\b(\d{6})\b', "6位数字"),
            (r'\b(\d{4})\b', "4位数字"),
            (r'\b(\d{8})\b', "8位数字"),
            (r'\b([A-Z0-9]{6})\b', "6位字母数字组合"),
            (r'\b([A-Z0-9]{4})\b', "4位字母数字组合"),
        ]

        # 按优先级尝试匹配
        for pattern, desc in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                # 获取最后一个匹配的验证码（通常是最新的）
                code = matches[-1]
                if self._is_valid_verification_code(code):
                    return code

        # 如果上述模式都没匹配到，尝试更宽松的匹配
        all_numbers = re.findall(r'\d{4,8}', text)
        for num in all_numbers:
            if self._is_valid_verification_code(num):
                return num

        return None

    def _is_valid_verification_code(self, code):
        """验证验证码是否有效"""
        # 长度检查
        if len(code) < 4 or len(code) > 8:
            return False

        # 排除明显不是验证码的数字
        exclude_patterns = [
            r'^20\d{2}$',     # 年份 (2000-2099)
            r'^19\d{2}$',     # 年份 (1900-1999)
            r'^1[3-9]\d{9}$', # 手机号码
            r'^0\d{10,11}$',  # 固定电话
        ]

        for pattern in exclude_patterns:
            if re.match(pattern, code):
                return False

        return True

    def wait_for_verification_code(self, wait_seconds=20):
        """等待验证码发送的倒计时"""
        for i in range(wait_seconds, 0, -1):
            if not self.running:
                break
            self.countdown_update.emit(i)
            time.sleep(1)

    def stop(self):
        """停止监控"""
        self.running = False


class AutoLoginWorker(QThread):
    """自动登录工作线程 - 支持完整自动化"""

    # 信号定义
    status_changed = pyqtSignal(str)    # 状态变化
    log_message = pyqtSignal(str)       # 日志消息
    countdown_update = pyqtSignal(int)  # 倒计时更新
    verification_code_needed = pyqtSignal()  # 需要验证码

    def __init__(self, email_dialog, target_email=None):
        super().__init__()
        self.email_dialog = email_dialog
        self.driver = None
        self.running = True
        self.waiting_for_verification_code = False  # 标志是否正在等待验证码
        self.target_email = target_email  # 目标邮箱地址

    def run(self):
        """运行完整自动登录流程"""
        try:
            if not SELENIUM_AVAILABLE:
                self.log_message.emit("❌ Selenium未安装，使用基础浏览器启动模式")
                self._launch_basic_browser()
                return

            self.log_message.emit("🚀 开始自动登录流程")
            self.status_changed.emit("正在启动自动化浏览器...")

            # 启动Selenium控制的Chrome浏览器
            self._setup_selenium_driver()

            # 执行自动登录流程
            self._execute_auto_login()

        except Exception as e:
            self.status_changed.emit(f"自动登录失败: {str(e)}")
            self.log_message.emit(f"❌ 自动登录失败: {str(e)}")
            # 如果Selenium失败，回退到基础模式
            self._launch_basic_browser()

    def _setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--incognito")  # 无痕模式
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.log_message.emit("✅ 自动化浏览器已启动")

        except Exception as e:
            raise Exception(f"启动Selenium浏览器失败: {str(e)}")

    def _execute_auto_login(self):
        """执行自动登录流程"""
        try:
            # 1. 访问登录页面
            self.log_message.emit("🌐 正在访问 AugmentCode 网站...")
            self.driver.get("https://app.augmentcode.com")

            # 等待页面加载
            time.sleep(3)

            # 2. 查找并点击登录按钮
            self._find_and_click_login_button()

            # 3. 输入邮箱地址
            self._input_email_address()

            # 4. 点击发送验证码
            self._click_send_verification_code()

            # 5. 等待验证码并自动填写
            self._wait_and_fill_verification_code()

        except Exception as e:
            self.log_message.emit(f"❌ 自动登录流程失败: {str(e)}")
            raise

    def _find_and_click_login_button(self):
        """查找并点击登录按钮"""
        try:
            self.log_message.emit("🔍 查找登录按钮...")

            # 常见的登录按钮选择器
            login_selectors = [
                "a[href*='login']",
                "button:contains('登录')",
                "button:contains('Login')",
                "button:contains('Sign in')",
                ".login-btn",
                "#login-btn"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    if ":contains" in selector:
                        # 手动查找包含文本的按钮
                        buttons = self.driver.find_elements(By.TAG_NAME, "button")
                        for btn in buttons:
                            if any(text in btn.text.lower() for text in ['login', 'sign in', '登录']):
                                login_button = btn
                                break
                    else:
                        login_button = WebDriverWait(self.driver, 2).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    if login_button:
                        break
                except:
                    continue

            if login_button:
                self.log_message.emit("✅ 找到登录按钮，正在点击...")
                login_button.click()
                time.sleep(2)
            else:
                # 如果没找到登录按钮，可能已经在登录页面
                self.log_message.emit("ℹ️ 未找到登录按钮，可能已在登录页面")

        except Exception as e:
            self.log_message.emit(f"⚠️ 查找登录按钮失败: {str(e)}")

    def _input_email_address(self):
        """输入邮箱地址"""
        try:
            self.log_message.emit("📧 查找邮箱输入框...")

            # 根据提供的HTML元素信息
            email_selectors = [
                "#username",  # 从HTML中获取的ID
                "input[name='username']",
                "input[type='email']",
                "input[inputmode='email']",
                ".input.c5f1fe555.c925e4d05",  # 从HTML中获取的class
                "input[autocomplete='email']"
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if email_input:
                        break
                except:
                    continue

            if email_input:
                # 使用目标邮箱地址或配置的邮箱地址
                email_address = self.target_email or EMAIL_CONFIG.get('email', '<EMAIL>')
                if email_address == '<EMAIL>':
                    self.log_message.emit("❌ 请先配置正确的邮箱地址或输入目标邮箱")
                    return

                self.log_message.emit(f"✅ 找到邮箱输入框，正在输入: {email_address}")

                # 清空并输入邮箱
                email_input.clear()
                email_input.send_keys(email_address)

                self.log_message.emit("✅ 邮箱地址输入完成")
                time.sleep(1)
            else:
                self.log_message.emit("❌ 未找到邮箱输入框")

        except Exception as e:
            self.log_message.emit(f"❌ 输入邮箱地址失败: {str(e)}")

    def _click_send_verification_code(self):
        """点击发送验证码按钮 - 修复时序问题"""
        try:
            self.log_message.emit("🔍 查找发送验证码按钮...")

            # 🔥 简化的人机验证处理逻辑
            self.log_message.emit("🤖 开始处理人机验证...")

            # 首先尝试点击人机验证元素
            verification_clicked = self._click_human_verification_element()

            if verification_clicked:
                self.log_message.emit("✅ 已点击人机验证元素，等待验证完成...")
            else:
                self.log_message.emit("⚠️ 未找到人机验证元素，可能已经完成或需要手动处理")

            # 查找Continue按钮 - 根据您提供的HTML结构
            send_code_selectors = [
                'button[type="submit"][name="action"][value="default"]',  # 您提供的具体按钮
                'button[data-action-button-primary="true"]',              # 主要操作按钮
                "button[type='submit']",
                ".continue-btn",
                ".send-btn"
            ]

            send_button = None
            for selector in send_code_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # 检查按钮文本是否包含Continue
                            btn_text = element.text.lower()
                            if 'continue' in btn_text or not btn_text:  # 空文本也可能是Continue按钮
                                send_button = element
                                self.log_message.emit(f"✅ 找到发送按钮: {selector}")
                                break

                    if send_button:
                        break
                except Exception:
                    continue

            # 如果没找到特定按钮，查找所有可能的提交按钮
            if not send_button:
                try:
                    buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for btn in buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            btn_text = btn.text.lower()
                            if any(text in btn_text for text in ['continue', 'send', '发送', '继续', 'submit']):
                                send_button = btn
                                self.log_message.emit(f"✅ 通过文本找到发送按钮: {btn.text}")
                                break
                except Exception:
                    pass

            if send_button:
                # 🔥 最新修复：20秒+再次点击+20秒的简化流程
                self.log_message.emit("🔍 等待人机验证完成...")

                # 使用简化的20秒+再次点击+20秒逻辑
                verification_completed = self._wait_for_verification_truly_complete()

                if verification_completed:
                    self.log_message.emit("✅ 人机验证流程已完成，可以点击Continue按钮")
                else:
                    self.log_message.emit("⚠️ 验证流程异常，但尝试点击Continue按钮...")
                    time.sleep(2)

                # 修复stale element reference - 重新查找按钮
                self.log_message.emit("✅ 找到发送按钮，正在点击...")
                success = self._click_continue_button_safely()

                if success:
                    self.log_message.emit("📤 验证码发送请求已提交")
                    time.sleep(3)
                else:
                    self.log_message.emit("❌ 点击Continue按钮失败，请手动点击")
            else:
                self.log_message.emit("❌ 未找到发送验证码按钮")

        except Exception as e:
            self.log_message.emit(f"❌ 点击发送验证码按钮失败: {str(e)}")

    def _click_continue_button_safely(self):
        """安全地点击Continue按钮，避免stale element reference错误 - 增强版"""
        max_retries = 5  # 最多重试5次

        for retry in range(max_retries):
            try:
                self.log_message.emit(f"🔄 尝试点击Continue按钮 (第{retry + 1}次)")

                # 等待DOM稳定
                time.sleep(1)

                # 使用JavaScript直接查找和点击，避免Selenium元素引用
                success = self.driver.execute_script("""
                    // 查找Continue按钮的多种方式
                    var selectors = [
                        'button[type="submit"][name="action"][value="default"]',
                        'button[data-action-button-primary="true"]',
                        'button[type="submit"]'
                    ];

                    var button = null;

                    // 方法1: 使用CSS选择器
                    for (var i = 0; i < selectors.length; i++) {
                        var elements = document.querySelectorAll(selectors[i]);
                        for (var j = 0; j < elements.length; j++) {
                            var elem = elements[j];
                            if (elem.offsetParent !== null && !elem.disabled) {
                                var text = elem.textContent.toLowerCase();
                                if (text.includes('continue') || text.trim() === '') {
                                    button = elem;
                                    break;
                                }
                            }
                        }
                        if (button) break;
                    }

                    // 方法2: 查找所有按钮
                    if (!button) {
                        var allButtons = document.querySelectorAll('button');
                        for (var k = 0; k < allButtons.length; k++) {
                            var btn = allButtons[k];
                            if (btn.offsetParent !== null && !btn.disabled) {
                                var btnText = btn.textContent.toLowerCase();
                                if (btnText.includes('continue') || btnText.includes('send') ||
                                    btnText.includes('submit') || btnText.includes('发送')) {
                                    button = btn;
                                    break;
                                }
                            }
                        }
                    }

                    if (button) {
                        // 滚动到按钮位置
                        button.scrollIntoView({behavior: 'smooth', block: 'center'});

                        // 等待一下
                        setTimeout(function() {
                            // 点击按钮
                            button.click();

                            // 触发事件
                            button.dispatchEvent(new Event('click', { bubbles: true }));
                            button.dispatchEvent(new Event('mousedown', { bubbles: true }));
                            button.dispatchEvent(new Event('mouseup', { bubbles: true }));
                        }, 500);

                        return true;
                    }

                    return false;
                """)

                if success:
                    self.log_message.emit("✅ JavaScript成功点击Continue按钮")
                    time.sleep(2)  # 等待页面响应
                    return True
                else:
                    self.log_message.emit(f"⚠️ 第{retry + 1}次尝试未找到按钮")

            except Exception as e:
                self.log_message.emit(f"⚠️ 第{retry + 1}次尝试失败: {str(e)}")

            # 如果不是最后一次重试，等待后继续
            if retry < max_retries - 1:
                time.sleep(2)

        # 所有重试都失败，尝试最后的备用方案
        self.log_message.emit("🔄 尝试备用方案：直接提交表单")
        try:
            form_submitted = self.driver.execute_script("""
                // 查找表单并直接提交
                var forms = document.querySelectorAll('form');
                for (var i = 0; i < forms.length; i++) {
                    var form = forms[i];
                    if (form.offsetParent !== null) {
                        form.submit();
                        return true;
                    }
                }
                return false;
            """)

            if form_submitted:
                self.log_message.emit("✅ 备用方案成功：表单已提交")
                return True
            else:
                self.log_message.emit("❌ 所有方案都失败，请手动点击Continue按钮")
                return False

        except Exception as e:
            self.log_message.emit(f"❌ 备用方案也失败: {str(e)}")
            return False

    def _wait_and_fill_verification_code(self):
        """等待验证码并自动填写 - 增强版"""
        try:
            self.log_message.emit("⏰ 开始等待验证码...")

            # 发送信号通知需要验证码
            self.verification_code_needed.emit()

            # 设置标志表示正在等待验证码
            self.waiting_for_verification_code = True

            # 等待验证码的倒计时，同时检查验证码是否已到达
            wait_time = 60  # 增加等待时间到60秒
            for i in range(wait_time, 0, -1):
                if not self.running:
                    return

                # 每秒检查一次是否有验证码
                if hasattr(self, 'current_code') and self.current_code:
                    self.log_message.emit("✅ 检测到验证码已到达，立即填写...")
                    self.waiting_for_verification_code = False
                    self._try_fill_verification_code()
                    return

                self.countdown_update.emit(i)
                time.sleep(1)

            # 倒计时结束后最后尝试一次
            self.waiting_for_verification_code = False
            self.log_message.emit("⏰ 倒计时结束，最后尝试获取验证码...")
            self._try_fill_verification_code()

        except Exception as e:
            self.waiting_for_verification_code = False
            self.log_message.emit(f"❌ 等待验证码失败: {str(e)}")

    def _try_fill_verification_code(self):
        """尝试填写验证码 - 增强版"""
        try:
            # 检查邮箱监控是否有验证码 - 修复：检查正确的验证码存储位置
            verification_code = None

            # 方法1: 检查当前对象的current_code属性
            if hasattr(self, 'current_code') and self.current_code:
                verification_code = self.current_code
                self.log_message.emit(f"✅ 从当前对象获取到验证码: {verification_code}")

            # 方法2: 检查email_dialog的current_code属性（兼容性）
            elif hasattr(self, 'email_dialog') and hasattr(self.email_dialog, 'current_code') and self.email_dialog.current_code:
                verification_code = self.email_dialog.current_code
                self.log_message.emit(f"✅ 从email_dialog获取到验证码: {verification_code}")

            # 方法3: 检查父窗口的current_code属性
            elif hasattr(self, 'parent') and hasattr(self.parent(), 'current_code') and self.parent().current_code:
                verification_code = self.parent().current_code
                self.log_message.emit(f"✅ 从父窗口获取到验证码: {verification_code}")

            if verification_code:

                # 增强的验证码输入框查找
                code_selectors = [
                    "#code",
                    "input[name='code']",
                    "input[name*='verification']",
                    "input[name*='otp']",
                    "input[id*='code']",
                    "input[id*='verification']",
                    "input[id*='otp']",
                    "input[type='text']",
                    "input[type='number']",
                    "input[placeholder*='验证码']",
                    "input[placeholder*='code']",
                    "input[placeholder*='Code']",
                    "input[placeholder*='verification']",
                    "input[autocomplete='one-time-code']"
                ]

                code_input = None
                for selector in code_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                # 检查输入框是否为空或者看起来像验证码输入框
                                placeholder = element.get_attribute('placeholder') or ''
                                name = element.get_attribute('name') or ''
                                id_attr = element.get_attribute('id') or ''
                                current_value = element.get_attribute('value') or ''

                                # 优先选择明确的验证码相关输入框
                                if any(keyword in (placeholder + name + id_attr).lower()
                                       for keyword in ['code', 'verification', 'otp', '验证码']):
                                    code_input = element
                                    self.log_message.emit(f"✅ 找到验证码输入框: {selector}")
                                    break
                                # 如果没有明确标识，选择空的文本输入框
                                elif not current_value.strip() and selector in ["input[type='text']", "input[type='number']"]:
                                    code_input = element
                                    self.log_message.emit(f"✅ 找到空的输入框，假设为验证码输入框: {selector}")
                                    break

                        if code_input:
                            break
                    except Exception:
                        continue

                if code_input:
                    try:
                        # 滚动到输入框位置
                        self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", code_input)
                        time.sleep(1)

                        self.log_message.emit("✅ 找到验证码输入框，正在填写...")

                        # 清空并填写验证码
                        code_input.clear()
                        time.sleep(0.5)
                        code_input.send_keys(verification_code)

                        # 触发输入事件
                        self.driver.execute_script("""
                            arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                            arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                        """, code_input)

                        time.sleep(1)
                        self.log_message.emit("✅ 验证码已填写")

                        # 查找并点击提交按钮
                        self._click_submit_button()
                    except Exception as e:
                        self.log_message.emit(f"❌ 填写验证码时出错: {str(e)}")
                else:
                    self.log_message.emit("❌ 未找到验证码输入框")
            else:
                self.log_message.emit("⚠️ 未获取到验证码，请手动输入")

        except Exception as e:
            self.log_message.emit(f"❌ 填写验证码失败: {str(e)}")

    def _click_submit_button(self):
        """点击提交按钮 - 增强版"""
        try:
            # 根据您提供的HTML结构，优化按钮查找
            submit_selectors = [
                'button[type="submit"][name="action"][value="default"]',  # 您提供的具体按钮结构
                'button[data-action-button-primary="true"]',              # 主要操作按钮
                'button[type="submit"]',
                'button[class*="button-login"]',                          # 登录相关按钮
                'button[class*="continue"]',
                'button[class*="submit"]',
                '.continue-btn',
                '.submit-btn'
            ]

            submit_button = None

            # 方法1: 使用CSS选择器查找
            for selector in submit_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            submit_button = element
                            self.log_message.emit(f"✅ 找到提交按钮: {selector}")
                            break

                    if submit_button:
                        break
                except Exception:
                    continue

            # 方法2: 如果没找到，通过按钮文本查找
            if not submit_button:
                try:
                    buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for btn in buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            btn_text = btn.text.lower()
                            if any(text in btn_text for text in ['continue', 'submit', 'verify', '提交', '确认', '登录', '下一步']):
                                submit_button = btn
                                self.log_message.emit(f"✅ 通过文本找到提交按钮: {btn.text}")
                                break
                except Exception:
                    pass

            # 方法3: 查找表单内的提交按钮
            if not submit_button:
                try:
                    forms = self.driver.find_elements(By.TAG_NAME, "form")
                    for form in forms:
                        form_buttons = form.find_elements(By.CSS_SELECTOR, 'button[type="submit"], input[type="submit"]')
                        for btn in form_buttons:
                            if btn.is_displayed() and btn.is_enabled():
                                submit_button = btn
                                self.log_message.emit("✅ 在表单中找到提交按钮")
                                break
                        if submit_button:
                            break
                except Exception:
                    pass

            if submit_button:
                # 使用安全的点击方法避免stale element reference
                self.log_message.emit("✅ 正在提交验证码...")
                success = self._click_submit_button_safely()

                if success:
                    self.log_message.emit("🎉 验证码提交完成！")
                    time.sleep(3)

                    # 检查是否登录成功或跳转到下一步
                    current_url = self.driver.current_url
                    if "app.augmentcode.com" in current_url:
                        self.log_message.emit("🎉 登录成功！已跳转到应用页面")
                        self.status_changed.emit("登录成功")
                    elif "login" not in current_url.lower():
                        self.log_message.emit("✅ 页面已跳转，可能登录成功")
                        self.status_changed.emit("登录成功")
                    else:
                        self.log_message.emit("⚠️ 仍在登录页面，请检查验证码是否正确")
                else:
                    self.log_message.emit("❌ 提交验证码失败，请手动点击提交")
            else:
                self.log_message.emit("❌ 未找到提交按钮，请手动点击提交")

        except Exception as e:
            self.log_message.emit(f"❌ 点击提交按钮失败: {str(e)}")

    def _click_submit_button_safely(self):
        """安全地点击提交按钮，避免stale element reference错误 - 增强版"""
        max_retries = 5  # 最多重试5次

        for retry in range(max_retries):
            try:
                self.log_message.emit(f"🔄 尝试点击提交按钮 (第{retry + 1}次)")

                # 等待DOM稳定
                time.sleep(1)

                # 使用JavaScript直接查找和点击，避免Selenium元素引用
                success = self.driver.execute_script("""
                    // 查找提交按钮的多种方式
                    var selectors = [
                        'button[type="submit"][name="action"][value="default"]',
                        'button[data-action-button-primary="true"]',
                        'button[type="submit"]',
                        'button[class*="button-login"]',
                        'button[class*="continue"]',
                        'button[class*="submit"]'
                    ];

                    var button = null;

                    // 方法1: 使用CSS选择器
                    for (var i = 0; i < selectors.length; i++) {
                        var elements = document.querySelectorAll(selectors[i]);
                        for (var j = 0; j < elements.length; j++) {
                            var elem = elements[j];
                            if (elem.offsetParent !== null && !elem.disabled) {
                                button = elem;
                                break;
                            }
                        }
                        if (button) break;
                    }

                    // 方法2: 查找所有按钮
                    if (!button) {
                        var allButtons = document.querySelectorAll('button');
                        for (var k = 0; k < allButtons.length; k++) {
                            var btn = allButtons[k];
                            if (btn.offsetParent !== null && !btn.disabled) {
                                var btnText = btn.textContent.toLowerCase();
                                if (btnText.includes('continue') || btnText.includes('submit') ||
                                    btnText.includes('verify') || btnText.includes('提交') ||
                                    btnText.includes('确认') || btnText.includes('登录')) {
                                    button = btn;
                                    break;
                                }
                            }
                        }
                    }

                    // 方法3: 查找表单内的提交按钮
                    if (!button) {
                        var forms = document.querySelectorAll('form');
                        for (var l = 0; l < forms.length; l++) {
                            var formButtons = forms[l].querySelectorAll('button[type="submit"], input[type="submit"]');
                            for (var m = 0; m < formButtons.length; m++) {
                                var formBtn = formButtons[m];
                                if (formBtn.offsetParent !== null && !formBtn.disabled) {
                                    button = formBtn;
                                    break;
                                }
                            }
                            if (button) break;
                        }
                    }

                    if (button) {
                        // 滚动到按钮位置
                        button.scrollIntoView({behavior: 'smooth', block: 'center'});

                        // 等待一下
                        setTimeout(function() {
                            // 点击按钮
                            button.click();

                            // 触发事件
                            button.dispatchEvent(new Event('click', { bubbles: true }));
                            button.dispatchEvent(new Event('mousedown', { bubbles: true }));
                            button.dispatchEvent(new Event('mouseup', { bubbles: true }));
                        }, 500);

                        return true;
                    }

                    return false;
                """)

                if success:
                    self.log_message.emit("✅ JavaScript成功点击提交按钮")
                    time.sleep(2)  # 等待页面响应
                    return True
                else:
                    self.log_message.emit(f"⚠️ 第{retry + 1}次尝试未找到提交按钮")

            except Exception as e:
                self.log_message.emit(f"⚠️ 第{retry + 1}次尝试失败: {str(e)}")

            # 如果不是最后一次重试，等待后继续
            if retry < max_retries - 1:
                time.sleep(2)

        # 所有重试都失败，尝试最后的备用方案
        self.log_message.emit("🔄 尝试备用方案：直接提交表单")
        try:
            form_submitted = self.driver.execute_script("""
                // 查找表单并直接提交
                var forms = document.querySelectorAll('form');
                for (var i = 0; i < forms.length; i++) {
                    var form = forms[i];
                    if (form.offsetParent !== null) {
                        form.submit();
                        return true;
                    }
                }
                return false;
            """)

            if form_submitted:
                self.log_message.emit("✅ 备用方案成功：表单已提交")
                return True
            else:
                self.log_message.emit("❌ 所有方案都失败，请手动点击提交按钮")
                return False

        except Exception as e:
            self.log_message.emit(f"❌ 备用方案也失败: {str(e)}")
            return False

    def _launch_basic_browser(self):
        """启动基础浏览器（无自动化）"""
        try:
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                "chrome.exe"
            ]

            chrome_path = None
            for path in chrome_paths:
                if os.path.exists(path) or path == "chrome.exe":
                    chrome_path = path
                    break

            if chrome_path:
                cmd = [chrome_path, "--incognito", "--new-window", "https://app.augmentcode.com"]
                subprocess.Popen(cmd)
                self.log_message.emit("✅ 基础浏览器已启动")
                self.log_message.emit("📝 请手动完成登录流程")
            else:
                self.log_message.emit("❌ 未找到Chrome浏览器")

        except Exception as e:
            self.log_message.emit(f"❌ 启动基础浏览器失败: {str(e)}")

    def _click_human_verification_element(self):
        """点击人机验证元素 - 简化版"""
        try:
            # 等待页面稳定
            time.sleep(2)

            # 方法1: 查找并点击checkbox（根据用户提供的HTML）
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                        self.log_message.emit("🖱️ 找到未选中的验证checkbox，正在点击...")

                        # 滚动到checkbox位置
                        self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", checkbox)
                        time.sleep(1)

                        # 点击checkbox
                        checkbox.click()

                        # 触发事件
                        self.driver.execute_script("""
                            arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                            arguments[0].dispatchEvent(new Event('click', { bubbles: true }));
                        """, checkbox)

                        self.log_message.emit("✅ 已点击验证checkbox")
                        return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 点击checkbox失败: {str(e)}")

            # 方法2: 查找并点击验证容器
            try:
                verification_containers = [
                    '.ulp-captcha-container',
                    '.cf-turnstile',
                    '.captcha-container',
                    '[data-sitekey]'
                ]

                for selector in verification_containers:
                    containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for container in containers:
                        if container.is_displayed():
                            self.log_message.emit(f"🖱️ 找到验证容器: {selector}，正在点击...")
                            container.click()
                            self.log_message.emit("✅ 已点击验证容器")
                            return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 点击验证容器失败: {str(e)}")

            return False

        except Exception as e:
            self.log_message.emit(f"❌ 点击人机验证元素失败: {str(e)}")
            return False

    def _handle_human_verification(self):
        """处理人机验证 - 修复时序问题，等待验证完成"""
        try:
            self.log_message.emit("🤖 开始处理人机验证...")

            # 首先检查是否有"Verify you are human"文本的复选框
            verification_checkbox = None

            # 方法1: 查找所有复选框，检查其父元素或相邻元素是否包含"Verify you are human"文本
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                        # 检查复选框的父元素或相邻元素是否包含验证文本
                        parent = checkbox.find_element(By.XPATH, '..')
                        parent_text = parent.text.lower()

                        if any(text in parent_text for text in ['verify you are human', 'verify', 'human', 'captcha']):
                            verification_checkbox = checkbox
                            self.log_message.emit("✅ 找到'Verify you are human'复选框")
                            break

                        # 检查更上层的父元素
                        try:
                            grandparent = parent.find_element(By.XPATH, '..')
                            grandparent_text = grandparent.text.lower()
                            if any(text in grandparent_text for text in ['verify you are human', 'verify', 'human']):
                                verification_checkbox = checkbox
                                self.log_message.emit("✅ 找到验证复选框（通过祖父元素文本）")
                                break
                        except:
                            pass
            except Exception as e:
                self.log_message.emit(f"⚠️ 方法1查找失败: {str(e)}")

            # 方法2: 如果方法1失败，查找页面上所有未选中的复选框
            if not verification_checkbox:
                try:
                    checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                    for checkbox in checkboxes:
                        if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                            verification_checkbox = checkbox
                            self.log_message.emit("✅ 找到未选中的复选框")
                            break
                except Exception as e:
                    self.log_message.emit(f"⚠️ 方法2查找失败: {str(e)}")

            # 方法3: 查找Cloudflare Turnstile或其他验证容器
            if not verification_checkbox:
                verification_containers = [
                    '.ulp-captcha-container',
                    '.cf-turnstile',
                    '.captcha-container',
                    '[data-sitekey]'
                ]

                for selector in verification_containers:
                    try:
                        containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for container in containers:
                            if container.is_displayed():
                                # 在容器内查找复选框
                                inner_checkboxes = container.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                                for inner_cb in inner_checkboxes:
                                    if inner_cb.is_displayed() and inner_cb.is_enabled() and not inner_cb.is_selected():
                                        verification_checkbox = inner_cb
                                        self.log_message.emit(f"✅ 在验证容器中找到复选框: {selector}")
                                        break

                                # 如果容器内没有复选框，尝试点击容器本身并等待验证完成
                                if not verification_checkbox:
                                    self.log_message.emit(f"🖱️ 尝试点击验证容器: {selector}")
                                    container.click()

                                    # 关键修复：给Cloudflare Turnstile足够时间初始化
                                    self.log_message.emit("⏰ 等待Cloudflare Turnstile初始化...")
                                    time.sleep(3)  # 等待3秒让验证组件完全加载

                                    # 等待验证完成 - 使用增强的检测方法
                                    self.log_message.emit("⏰ 开始监控人机验证状态...")
                                    verification_completed = self._wait_for_human_verification_completion()

                                    if verification_completed:
                                        self.log_message.emit("✅ 人机验证已完成")
                                        return True
                                    else:
                                        self.log_message.emit("⚠️ 人机验证可能需要手动完成")
                                        return False

                        if verification_checkbox:
                            break
                    except Exception as e:
                        continue

            # 如果找到复选框，进行点击操作
            if verification_checkbox:
                try:
                    # 滚动到复选框位置
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", verification_checkbox)
                    time.sleep(1)

                    # 点击复选框
                    self.log_message.emit("🖱️ 正在点击人机验证复选框...")
                    verification_checkbox.click()

                    # 触发事件
                    self.driver.execute_script("""
                        arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                        arguments[0].dispatchEvent(new Event('click', { bubbles: true }));
                    """, verification_checkbox)

                    time.sleep(2)

                    # 验证是否成功选中
                    if verification_checkbox.is_selected():
                        self.log_message.emit("✅ 人机验证复选框已成功选中")
                        return True
                    else:
                        self.log_message.emit("⚠️ 复选框点击后仍未选中，等待验证完成...")
                        # 等待验证完成
                        verification_completed = self._wait_for_human_verification_completion()
                        return verification_completed

                except Exception as e:
                    self.log_message.emit(f"❌ 点击验证复选框失败: {str(e)}")
            else:
                self.log_message.emit("ℹ️ 未找到人机验证复选框，可能已经完成或不需要验证")

        except Exception as e:
            self.log_message.emit(f"❌ 处理人机验证失败: {str(e)}")

        return False

    def _check_human_verification_needed(self):
        """检查是否需要人机验证"""
        try:
            # 检查是否有验证相关元素
            verification_indicators = [
                '.cf-turnstile',
                '.captcha',
                '.verification',
                '[data-sitekey]',
                'iframe[src*="cloudflare"]',
                'iframe[src*="turnstile"]',
                'iframe[src*="captcha"]',
                'input[type="checkbox"]'
            ]

            for selector in verification_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            return True
                except:
                    continue

            return False

        except Exception:
            return False

    def _check_human_verification_needed(self):
        """检查是否需要人机验证"""
        try:
            # 检查是否有未选中的验证复选框
            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
            for checkbox in checkboxes:
                if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                    parent = checkbox.find_element(By.XPATH, '..')
                    parent_text = parent.text.lower()
                    if any(text in parent_text for text in ['verify you are human', 'verify', 'human', 'captcha']):
                        return True

            # 检查是否有验证容器
            verification_containers = self.driver.find_elements(By.CSS_SELECTOR,
                '.ulp-captcha-container, .cf-turnstile, .captcha-container')
            for container in verification_containers:
                if container.is_displayed():
                    return True

            return False
        except Exception:
            return False

    def _wait_for_human_verification_completion(self):
        """等待人机验证完成 - 增强版，检测Success!标识"""
        try:
            self.log_message.emit("⏰ 开始等待Cloudflare Turnstile验证完成...")

            max_wait_time = 60  # 60秒等待时间
            check_interval = 2   # 每2秒检查一次

            for i in range(0, max_wait_time, check_interval):
                if not self.running:
                    break

                # 方法1: 检查页面是否显示"Success!"文本（最可靠的方法）
                try:
                    page_text = self.driver.find_element(By.TAG_NAME, "body").text
                    if "Success!" in page_text:
                        self.log_message.emit("✅ 检测到Success标识，验证成功")
                        return True
                except:
                    pass

                # 方法2: 检查是否有成功的CSS类或属性
                try:
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR,
                        '[class*="success"], [class*="completed"], [class*="verified"]')
                    if success_elements:
                        for elem in success_elements:
                            if elem.is_displayed() and ("success" in elem.text.lower() or "✓" in elem.text):
                                self.log_message.emit("✅ 检测到成功元素，验证完成")
                                return True
                except:
                    pass

                # 方法3: 检查验证复选框是否已选中
                try:
                    checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                    for checkbox in checkboxes:
                        if checkbox.is_displayed() and checkbox.is_selected():
                            self.log_message.emit("✅ 检测到复选框已选中，验证完成")
                            return True
                except:
                    pass

                # 方法4: 检查Cloudflare token是否已生成
                verification_status = self._check_verification_status_comprehensive()
                if verification_status == "completed":
                    self.log_message.emit("✅ 人机验证已完成（token已生成）")
                    return True
                elif verification_status == "in_progress":
                    remaining = max_wait_time - i - check_interval
                    if remaining > 0:
                        self.log_message.emit(f"⏰ Cloudflare验证进行中... ({remaining}秒)")
                elif verification_status == "success_detected":
                    self.log_message.emit("✅ 检测到Success标识，等待token生成...")

                    # 关键修复：检测到Success后等待token生成
                    token_wait_time = 15  # 等待15秒让token生成
                    for token_wait in range(token_wait_time):
                        time.sleep(1)
                        # 再次检查是否有token
                        token_status = self._check_verification_status_comprehensive()
                        if token_status == "completed":
                            self.log_message.emit("✅ Success后token已生成，验证真正完成")
                            return True

                        remaining_token = token_wait_time - token_wait - 1
                        if remaining_token > 0:
                            self.log_message.emit(f"⏰ 等待token生成... ({remaining_token}秒)")

                    # 如果等待token超时，仍然认为验证成功
                    self.log_message.emit("⚠️ token等待超时，但Success已显示，认为验证成功")
                    return True
                else:
                    remaining = max_wait_time - i - check_interval
                    if remaining > 0:
                        self.log_message.emit(f"⏰ 等待人机验证完成... ({remaining}秒)")

                time.sleep(check_interval)

            # 超时后进行最后一次检查
            final_status = self._check_verification_status_comprehensive()
            if final_status in ["completed", "success_detected"]:
                self.log_message.emit("✅ 最终检查：人机验证已完成")
                return True

            self.log_message.emit("⚠️ 人机验证等待超时，但可能已完成")
            return False

        except Exception as e:
            self.log_message.emit(f"❌ 等待人机验证失败: {str(e)}")
            return False

    def _wait_for_verification_truly_complete(self):
        """等待人机验证真正完成 - 优化版：20秒+再次点击+20秒"""
        try:
            self.log_message.emit("🔍 开始人机验证等待流程...")

            # 第一次等待20秒
            self.log_message.emit("⏰ 第一次等待20秒，让人机验证加载...")
            for i in range(20):
                if not self.running:
                    return False

                remaining = 20 - i
                self.log_message.emit(f"⏰ 第一次等待中... ({remaining}秒)")
                time.sleep(1)

            # 再次点击验证容器
            self.log_message.emit("🖱️ 20秒后，再次点击验证容器...")
            self._click_verification_container_simple()

            # 第二次等待20秒
            self.log_message.emit("⏰ 第二次等待20秒，确保验证完成...")
            for i in range(20):
                if not self.running:
                    return False

                remaining = 20 - i
                self.log_message.emit(f"⏰ 第二次等待中... ({remaining}秒)")
                time.sleep(1)

            self.log_message.emit("✅ 验证等待流程完成，可以点击Continue按钮")
            return True

        except Exception as e:
            self.log_message.emit(f"❌ 验证等待失败: {str(e)}")
            return True  # 即使失败也继续执行

    def _click_verification_container_simple(self):
        """简化版：再次点击验证容器"""
        try:
            self.log_message.emit("🖱️ 查找验证容器...")

            # 查找 .ulp-captcha-container
            try:
                containers = self.driver.find_elements(By.CSS_SELECTOR, '.ulp-captcha-container')
                for container in containers:
                    if container.is_displayed():
                        self.log_message.emit("🖱️ 找到 .ulp-captcha-container，正在点击...")
                        container.click()
                        self.log_message.emit("✅ 已再次点击验证容器")
                        time.sleep(2)
                        return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 点击 .ulp-captcha-container 失败: {str(e)}")

            # 如果没有找到，尝试点击其他验证元素
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled():
                        self.log_message.emit("🖱️ 找到验证checkbox，正在点击...")
                        checkbox.click()
                        self.log_message.emit("✅ 已再次点击验证checkbox")
                        time.sleep(2)
                        return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 点击checkbox失败: {str(e)}")

            self.log_message.emit("⚠️ 未找到可点击的验证元素")
            return False

        except Exception as e:
            self.log_message.emit(f"❌ 再次点击验证容器失败: {str(e)}")
            return False

    def _check_verification_container_status(self):
        """检查验证容器的状态是否为成功状态"""
        try:
            self.log_message.emit("🔍 检查验证容器状态...")

            # 方法1: 检查页面是否显示Success文本
            try:
                page_text = self.driver.find_element(By.TAG_NAME, "body").text
                if "Success!" in page_text or "success" in page_text.lower():
                    self.log_message.emit("✅ 检测到Success文本，验证成功")
                    return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 检查Success文本失败: {str(e)}")

            # 方法2: 检查验证复选框是否已选中
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_selected():
                        self.log_message.emit("✅ 检测到复选框已选中，验证成功")
                        return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 检查复选框状态失败: {str(e)}")

            # 方法3: 检查Continue按钮是否可用
            try:
                continue_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                    'button[type="submit"][name="action"][value="default"]')
                for btn in continue_buttons:
                    if btn.is_displayed() and btn.is_enabled():
                        btn_classes = btn.get_attribute('class') or ''
                        if 'disabled' not in btn_classes.lower():
                            self.log_message.emit("✅ 检测到Continue按钮可用，验证可能成功")
                            return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 检查Continue按钮状态失败: {str(e)}")

            # 方法4: 检查Cloudflare Turnstile状态
            try:
                turnstile_status = self.driver.execute_script("""
                    // 检查Turnstile状态
                    var turnstileElements = document.querySelectorAll('.cf-turnstile');
                    for (var i = 0; i < turnstileElements.length; i++) {
                        var elem = turnstileElements[i];
                        var state = elem.getAttribute('data-state');
                        if (state === 'success' || state === 'verified') {
                            return 'success';
                        }
                    }

                    // 检查是否有token
                    var tokenInputs = document.querySelectorAll('input[name="cf-turnstile-response"]');
                    for (var j = 0; j < tokenInputs.length; j++) {
                        var token = tokenInputs[j].value;
                        if (token && token.length > 20) {
                            return 'token_found';
                        }
                    }

                    return 'unknown';
                """)

                if turnstile_status in ['success', 'token_found']:
                    self.log_message.emit("✅ 检测到Turnstile验证成功")
                    return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 检查Turnstile状态失败: {str(e)}")

            self.log_message.emit("⚠️ 验证容器状态检查未通过")
            return False

        except Exception as e:
            self.log_message.emit(f"❌ 检查验证容器状态失败: {str(e)}")
            return False

    def _click_verification_container_again(self):
        """重新点击验证容器"""
        try:
            self.log_message.emit("🖱️ 重新点击验证容器...")

            # 查找并点击验证容器
            verification_containers = [
                '.ulp-captcha-container',
                '.cf-turnstile',
                '.captcha-container',
                '[data-sitekey]'
            ]

            for selector in verification_containers:
                try:
                    containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for container in containers:
                        if container.is_displayed():
                            self.log_message.emit(f"🖱️ 重新点击验证容器: {selector}")

                            # 滚动到容器位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", container)
                            time.sleep(1)

                            # 点击容器
                            container.click()

                            self.log_message.emit("✅ 已重新点击验证容器")
                            time.sleep(2)  # 等待点击生效
                            return True
                except Exception as e:
                    self.log_message.emit(f"⚠️ 重新点击{selector}失败: {str(e)}")
                    continue

            # 如果没有找到验证容器，尝试点击checkbox
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled():
                        self.log_message.emit("🖱️ 重新点击验证checkbox")
                        checkbox.click()
                        self.log_message.emit("✅ 已重新点击验证checkbox")
                        time.sleep(2)
                        return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 重新点击checkbox失败: {str(e)}")

            self.log_message.emit("⚠️ 未找到可重新点击的验证元素")
            return False

        except Exception as e:
            self.log_message.emit(f"❌ 重新点击验证容器失败: {str(e)}")
            return False

    def _handle_potential_second_verification(self):
        """处理可能的二次验证确认 - 针对AugmentCode网站的特殊机制"""
        try:
            self.log_message.emit("🔍 检查是否需要二次验证确认...")

            # 等待2秒让页面稳定
            time.sleep(2)

            # 检查是否有需要二次点击的验证元素
            try:
                # 查找可能需要二次确认的checkbox
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled():
                        # 检查checkbox的状态
                        is_checked = checkbox.is_selected()

                        if not is_checked:
                            self.log_message.emit("🖱️ 发现未选中的验证checkbox，进行二次确认...")

                            # 滚动到checkbox位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", checkbox)
                            time.sleep(1)

                            # 二次点击确认
                            checkbox.click()

                            # 触发事件
                            self.driver.execute_script("""
                                arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                                arguments[0].dispatchEvent(new Event('click', { bubbles: true }));
                            """, checkbox)

                            self.log_message.emit("✅ 已完成二次验证确认")
                            time.sleep(2)  # 等待确认生效
                            return True
                        else:
                            self.log_message.emit("✅ 验证checkbox已选中，无需二次确认")
                            return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 二次验证确认失败: {str(e)}")

            # 检查是否有其他需要确认的验证元素
            try:
                # 查找可能的验证确认按钮
                confirm_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                    'button[class*="verify"], button[class*="confirm"], .cf-turnstile button')

                for btn in confirm_buttons:
                    if btn.is_displayed() and btn.is_enabled():
                        btn_text = btn.text.lower()
                        if any(keyword in btn_text for keyword in ['verify', 'confirm', 'check']):
                            self.log_message.emit(f"🖱️ 发现验证确认按钮: {btn.text}，正在点击...")
                            btn.click()
                            self.log_message.emit("✅ 已点击验证确认按钮")
                            time.sleep(2)
                            return True
            except Exception as e:
                self.log_message.emit(f"⚠️ 查找确认按钮失败: {str(e)}")

            return False

        except Exception as e:
            self.log_message.emit(f"❌ 处理二次验证确认失败: {str(e)}")
            return False

    def _final_continue_button_check(self):
        """最终检查Continue按钮是否真正可用"""
        try:
            self.log_message.emit("🔍 进行Continue按钮最终检查...")

            # 查找Continue按钮
            continue_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                'button[type="submit"][name="action"][value="default"]')

            for btn in continue_buttons:
                if btn.is_displayed() and btn.is_enabled():
                    # 检查按钮类名
                    btn_classes = btn.get_attribute('class') or ''
                    if 'disabled' in btn_classes.lower():
                        self.log_message.emit("⚠️ Continue按钮显示为禁用状态")
                        return False

                    # 检查按钮文本
                    btn_text = btn.text.strip()
                    if btn_text.lower() in ['continue', '继续', 'submit', '提交']:
                        self.log_message.emit(f"✅ Continue按钮状态正常: {btn_text}")

                        # 检查页面是否还有验证相关的加载状态
                        page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
                        if any(keyword in page_text for keyword in ['loading', 'verifying', 'processing']):
                            self.log_message.emit("⚠️ 页面仍在处理中，等待完成...")
                            return False

                        return True

            self.log_message.emit("⚠️ 未找到可用的Continue按钮")
            return False

        except Exception as e:
            self.log_message.emit(f"❌ Continue按钮最终检查失败: {str(e)}")
            return False

    def _check_verification_status_comprehensive(self):
        """全面检查验证状态，返回详细状态"""
        try:
            # 方法1: 优先检查Cloudflare Turnstile token（最可靠的方法）
            try:
                turnstile_status = self.driver.execute_script("""
                    // 方法1: 检查Turnstile token（最重要）
                    var tokenInputs = document.querySelectorAll('input[name="cf-turnstile-response"]');
                    for (var j = 0; j < tokenInputs.length; j++) {
                        var token = tokenInputs[j].value;
                        if (token && token.length > 20) {
                            return 'token_completed';  // 有token说明真正完成
                        }
                    }

                    // 方法2: 检查Turnstile状态
                    var turnstileElements = document.querySelectorAll('.cf-turnstile');
                    for (var i = 0; i < turnstileElements.length; i++) {
                        var elem = turnstileElements[i];
                        var state = elem.getAttribute('data-state');
                        if (state === 'success' || state === 'verified') {
                            return 'state_success';
                        }
                        if (state === 'verifying' || state === 'interactive') {
                            return 'in_progress';
                        }
                    }

                    // 方法3: 检查页面是否有Success文本（但不够可靠）
                    var bodyText = document.body.textContent || document.body.innerText || '';
                    if (bodyText.toLowerCase().includes('success')) {
                        return 'text_success';  // 仅文本成功，可能还需要等待
                    }

                    return 'unknown';
                """)

                if turnstile_status == 'token_completed':
                    return "completed"  # 真正完成
                elif turnstile_status == 'state_success':
                    return "completed"  # 状态完成
                elif turnstile_status == 'text_success':
                    return "success_detected"  # 仅文本成功，需要等待
                elif turnstile_status == 'in_progress':
                    return "in_progress"
            except Exception:
                pass

            # 方法2: 检查页面是否显示Success（备用方法）
            try:
                page_text = self.driver.find_element(By.TAG_NAME, 'body').text.lower()
                if 'success!' in page_text or 'success' in page_text:
                    return "success_detected"  # 需要进一步验证
            except Exception:
                pass

            # 方法3: 检查Success元素（备用方法）
            try:
                success_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Success')]")
                if success_elements:
                    for elem in success_elements:
                        if elem.is_displayed():
                            return "success_detected"  # 需要进一步验证
            except Exception:
                pass

            # 方法4: 检查复选框状态
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_selected():
                        parent = checkbox.find_element(By.XPATH, '..')
                        parent_text = parent.text.lower()
                        if any(text in parent_text for text in ['verify you are human', 'verify', 'human']):
                            return "completed"
            except Exception:
                pass

            # 方法5: 检查Continue按钮状态
            try:
                continue_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                    'button[type="submit"][name="action"][value="default"]')
                for button in continue_buttons:
                    if button.is_displayed() and button.is_enabled():
                        disabled = button.get_attribute('disabled')
                        aria_disabled = button.get_attribute('aria-disabled')
                        if not disabled and aria_disabled != 'true':
                            return "completed"
            except Exception:
                pass

            return "unknown"

        except Exception:
            return "unknown"

    def _is_human_verification_complete(self):
        """检查人机验证是否完成 - 修复版，不依赖按钮状态"""
        try:
            # 使用新的全面检测方法
            verification_status = self._check_verification_status_comprehensive()

            if verification_status == "success_detected":
                self.log_message.emit("✅ 检测到Success标识，验证已完成")
                return True
            elif verification_status == "completed":
                self.log_message.emit("✅ 检测到验证完成状态")
                return True

            # 方法1: 检查复选框是否已选中
            try:
                checkboxes = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_selected():
                        # 检查是否是验证相关的复选框
                        parent = checkbox.find_element(By.XPATH, '..')
                        parent_text = parent.text.lower()
                        if any(text in parent_text for text in ['verify you are human', 'verify', 'human', 'captcha']):
                            self.log_message.emit("✅ 检测到验证复选框已选中")
                            return True
            except Exception:
                pass

            # 方法2: 检查Cloudflare Turnstile验证状态
            try:
                # 检查Turnstile成功状态
                turnstile_success = self.driver.find_elements(By.CSS_SELECTOR,
                    '.cf-turnstile[data-state="success"], .cf-turnstile.success, [data-cf-turnstile-success="true"]')
                if turnstile_success:
                    self.log_message.emit("✅ 检测到Cloudflare Turnstile验证成功")
                    return True

                # 检查Turnstile隐藏token
                turnstile_tokens = self.driver.find_elements(By.CSS_SELECTOR,
                    'input[name="cf-turnstile-response"], input[name*="turnstile"]')
                for token in turnstile_tokens:
                    value = token.get_attribute('value')
                    if value and len(value) > 20:  # Turnstile token通常很长
                        self.log_message.emit("✅ 检测到Turnstile验证token")
                        return True
            except Exception:
                pass

            # 方法3: 检查验证容器状态变化
            try:
                verification_containers = self.driver.find_elements(By.CSS_SELECTOR, '.ulp-captcha-container')
                for container in verification_containers:
                    # 检查容器是否有成功状态的class
                    class_list = container.get_attribute('class') or ''
                    if any(status in class_list.lower() for status in ['success', 'completed', 'verified']):
                        self.log_message.emit("✅ 检测到验证容器成功状态")
                        return True

                    # 检查容器内是否有成功标识
                    container_text = container.text.lower()
                    if any(text in container_text for text in ['success', 'verified', 'complete']):
                        self.log_message.emit("✅ 检测到验证成功文本")
                        return True
            except Exception:
                pass

            # 注意：移除了Continue按钮检测，因为按钮可能在验证完成前就启用

            # 方法5: 检查页面是否有验证成功的隐藏字段
            try:
                hidden_inputs = self.driver.find_elements(By.CSS_SELECTOR,
                    'input[type="hidden"][name*="captcha"], input[type="hidden"][name*="verification"]')
                for hidden_input in hidden_inputs:
                    value = hidden_input.get_attribute('value')
                    if value and len(value) > 10:
                        self.log_message.emit("✅ 检测到验证隐藏字段有值")
                        return True
            except Exception:
                pass

            # 方法6: 检查网络请求状态（通过JavaScript）
            try:
                # 检查是否有验证成功的JavaScript变量
                verification_status = self.driver.execute_script("""
                    // 检查常见的验证状态变量
                    if (typeof window.captchaVerified !== 'undefined' && window.captchaVerified) return true;
                    if (typeof window.turnstileVerified !== 'undefined' && window.turnstileVerified) return true;
                    if (typeof window.verificationComplete !== 'undefined' && window.verificationComplete) return true;

                    // 检查Turnstile回调是否被调用
                    if (typeof window.turnstileCallback !== 'undefined') return true;

                    return false;
                """)

                if verification_status:
                    self.log_message.emit("✅ 检测到JavaScript验证状态")
                    return True
            except Exception:
                pass

            return False

        except Exception:
            return False

    def stop(self):
        """停止自动登录"""
        self.running = False
        if self.driver:
            try:
                self.driver.quit()
                self.log_message.emit("🔴 自动化浏览器已关闭")
            except:
                pass


class EmailDialog(QDialog):
    """PyQt6邮箱监控对话框"""

    def __init__(self, parent=None, config_manager=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.monitor_worker = None
        self.auto_login_worker = None
        self.current_code = None
        self.countdown_timer = None

        self._setup_dialog()
        self._setup_ui()
        self._connect_signals()

    def _setup_dialog(self):
        """设置对话框属性"""
        self.setWindowTitle(get_text("dialogs.titles.email_title"))
        self.setModal(True)
        self.setFixedSize(800, 600)  # 增加宽度以适应新布局

        # 居中显示
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)

    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("QQ邮箱验证码监控")
        title_label.setFont(get_title_font(16))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #4EA5FF; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 说明文本
        desc_label = QLabel("自动监控QQ邮箱中的验证码邮件，实时显示验证码信息")
        desc_label.setFont(get_default_font(10))
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #6b7280; margin-bottom: 15px;")
        layout.addWidget(desc_label)

        # 创建主要内容的水平布局
        main_layout = QHBoxLayout()

        # 左侧：控制和日志区域
        left_layout = QVBoxLayout()

        # 控制按钮区域
        self._create_control_buttons(left_layout)

        # 状态显示
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setFont(get_default_font(9))
        self.status_label.setStyleSheet("color: #6b7280; margin: 5px 0px;")
        left_layout.addWidget(self.status_label)

        # 监控日志区域
        log_label = QLabel("监控日志:")
        log_label.setFont(get_default_font(10, bold=True))
        log_label.setStyleSheet("color: #374151; margin-top: 10px;")
        left_layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setFont(get_monospace_font(9))
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                background-color: #f8fafc;
                padding: 8px;
            }
        """)
        left_layout.addWidget(self.log_text)

        # 右侧：验证码显示区域
        right_layout = QVBoxLayout()
        self._create_verification_code_area(right_layout)

        # 添加到主布局
        main_layout.addLayout(left_layout, 2)  # 左侧占2/3
        main_layout.addLayout(right_layout, 1)  # 右侧占1/3
        layout.addLayout(main_layout)

        # 关闭按钮
        self._create_close_button(layout)

    def _create_control_buttons(self, layout):
        """创建控制按钮区域"""
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始监控")
        self.start_btn.setFont(get_default_font(10))
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4EA5FF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3B8EF0;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)

        self.stop_btn = QPushButton("停止监控")
        self.stop_btn.setFont(get_default_font(10))
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #b91c1c;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)

        self.auto_login_btn = QPushButton("自动登录")
        self.auto_login_btn.setFont(get_default_font(10))
        self.auto_login_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.auto_login_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

    def _create_verification_code_area(self, layout):
        """创建验证码显示区域"""
        # 验证码区域标题
        code_label = QLabel("获取到的验证码:")
        code_label.setFont(get_default_font(10, bold=True))
        code_label.setStyleSheet("color: #374151; margin-bottom: 10px;")
        layout.addWidget(code_label)

        # 验证码显示框
        self.code_display = QLabel("暂无验证码")
        self.code_display.setFont(get_title_font(24))
        self.code_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.code_display.setStyleSheet("""
            QLabel {
                border: 2px solid #4EA5FF;
                border-radius: 8px;
                background-color: #f0f9ff;
                color: #1e40af;
                padding: 20px;
                margin: 10px 0px;
                font-weight: bold;
            }
        """)
        self.code_display.setMinimumHeight(80)
        layout.addWidget(self.code_display)

        # 复制按钮
        self.copy_btn = QPushButton("📋 复制验证码")
        self.copy_btn.setFont(get_default_font(10))
        self.copy_btn.setEnabled(False)
        self.copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)
        layout.addWidget(self.copy_btn)

        # 验证码信息显示
        info_label = QLabel("验证码信息:")
        info_label.setFont(get_default_font(9, bold=True))
        info_label.setStyleSheet("color: #374151; margin-top: 15px;")
        layout.addWidget(info_label)

        self.code_info = QLabel("等待验证码...")
        self.code_info.setFont(get_default_font(8))
        self.code_info.setWordWrap(True)
        self.code_info.setStyleSheet("""
            QLabel {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background-color: #f8fafc;
                padding: 8px;
                color: #6b7280;
            }
        """)
        self.code_info.setMinimumHeight(60)
        layout.addWidget(self.code_info)

        layout.addStretch()

    def _create_close_button(self, layout):
        """创建关闭按钮"""
        close_layout = QHBoxLayout()
        close_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.setFont(get_default_font(10))
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        close_layout.addWidget(self.close_btn)
        layout.addLayout(close_layout)

    def _connect_signals(self):
        """连接信号"""
        self.start_btn.clicked.connect(self._start_monitoring)
        self.stop_btn.clicked.connect(self._stop_monitoring)
        self.auto_login_btn.clicked.connect(self._start_auto_login)
        self.copy_btn.clicked.connect(self._copy_verification_code)
        self.close_btn.clicked.connect(self.close)

    def _start_monitoring(self):
        """开始监控"""
        if EMAIL_CONFIG['email'] == '<EMAIL>':
            self._add_log("❌ 请先在代码中配置正确的邮箱信息")
            return

        # 获取用户输入的目标邮箱
        target_email = get_target_email_from_user(self)
        if not target_email:
            self._add_log("❌ 未输入目标邮箱，取消监控")
            return

        self._add_log(f"📧 开始监控收信人: {target_email}")

        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        self.monitor_worker = EmailMonitorWorker(target_email)
        self.monitor_worker.message_received.connect(self._add_log)
        self.monitor_worker.status_changed.connect(self._update_status)
        self.monitor_worker.error_occurred.connect(self._handle_error)
        self.monitor_worker.verification_code_found.connect(self._on_verification_code_found)
        self.monitor_worker.countdown_update.connect(self._update_countdown)
        self.monitor_worker.start()

        self._add_log("🚀 开始启动邮箱监控...")

    def _start_auto_login(self):
        """启动自动登录"""
        if EMAIL_CONFIG['email'] == '<EMAIL>':
            self._add_log("❌ 请先在代码中配置正确的邮箱信息")
            return

        # 获取用户输入的目标邮箱
        target_email = get_target_email_from_user(self)
        if not target_email:
            self._add_log("❌ 未输入目标邮箱，取消自动登录")
            return

        self._add_log(f"🚀 开始自动登录，使用邮箱: {target_email}")

        # 保存目标邮箱供后续使用
        self.target_email = target_email

        # 如果邮箱监控未启动，先启动监控
        if not self.monitor_worker or not self.monitor_worker.isRunning():
            self._add_log("📧 自动启动邮箱监控...")
            self.monitor_worker = EmailMonitorWorker(target_email)
            self.monitor_worker.message_received.connect(self._add_log)
            self.monitor_worker.status_changed.connect(self._update_status)
            self.monitor_worker.error_occurred.connect(self._handle_error)
            self.monitor_worker.verification_code_found.connect(self._on_verification_code_found)
            self.monitor_worker.countdown_update.connect(self._update_countdown)
            self.monitor_worker.start()
            # 等待监控启动
            QTimer.singleShot(2000, self._continue_auto_login)
        else:
            self._continue_auto_login()

    def _continue_auto_login(self):
        """继续自动登录流程"""
        target_email = getattr(self, 'target_email', None)
        self.auto_login_worker = AutoLoginWorker(self, target_email)
        self.auto_login_worker.status_changed.connect(self._update_status)
        self.auto_login_worker.log_message.connect(self._add_log)
        self.auto_login_worker.countdown_update.connect(self._update_countdown)
        self.auto_login_worker.verification_code_needed.connect(self._on_verification_code_needed)
        self.auto_login_worker.start()

        # 禁用自动登录按钮，防止重复点击
        self.auto_login_btn.setEnabled(False)
        self.auto_login_btn.setText("登录中...")

        self._add_log("🚀 自动登录流程已启动")

    def _on_verification_code_needed(self):
        """当自动登录需要验证码时"""
        self._add_log("📧 自动登录流程需要验证码，开始监控邮箱...")
        # 确保邮箱监控正在运行
        if not self.monitor_worker or not self.monitor_worker.isRunning():
            self._start_monitoring()

    def _update_countdown(self, seconds):
        """更新倒计时显示"""
        self._add_log(f"⏰ 等待验证码发送 ({seconds}秒)...")

    def _stop_monitoring(self):
        """停止监控"""
        if self.monitor_worker:
            self.monitor_worker.stop()
            self.monitor_worker.wait()
            self.monitor_worker = None

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self._update_status("监控已停止")
        self._add_log("⏹️ 邮箱监控已停止")

    def _on_verification_code_found(self, code, sender, subject):
        """处理找到的验证码"""
        self.current_code = code

        # 更新验证码显示
        self.code_display.setText(code)
        self.code_display.setStyleSheet("""
            QLabel {
                border: 2px solid #10b981;
                border-radius: 8px;
                background-color: #ecfdf5;
                color: #065f46;
                padding: 20px;
                margin: 10px 0px;
                font-weight: bold;
            }
        """)

        # 启用复制按钮
        self.copy_btn.setEnabled(True)

        # 更新验证码信息
        info_text = f"发件人: {sender}\n主题: {subject}\n时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        self.code_info.setText(info_text)
        self.code_info.setStyleSheet("""
            QLabel {
                border: 1px solid #10b981;
                border-radius: 4px;
                background-color: #ecfdf5;
                padding: 8px;
                color: #065f46;
            }
        """)

        # 🔥 关键修复：验证码获取成功后立即尝试自动填写
        self._add_log(f"📧 收到验证码邮件")
        self._add_log(f"✅ 验证码: {code}")
        self._add_log(f"📤 发件人: {sender}")
        self._add_log(f"📋 主题: {subject}")

        # 立即尝试自动填写验证码（如果有活跃的浏览器会话）
        if hasattr(self, 'auto_login_worker') and self.auto_login_worker and self.auto_login_worker.isRunning():
            self._add_log("🤖 检测到自动登录进程，尝试自动填写验证码...")
            # 通过信号通知自动登录线程有新的验证码
            try:
                # 直接调用验证码填写方法
                QTimer.singleShot(1000, self._trigger_auto_fill_verification_code)
            except Exception as e:
                self._add_log(f"⚠️ 自动填写验证码失败: {str(e)}")
        else:
            self._add_log("💡 请手动复制验证码到浏览器，或等待自动填写")

    def _trigger_auto_fill_verification_code(self):
        """触发自动填写验证码"""
        try:
            if hasattr(self, 'auto_login_worker') and self.auto_login_worker and self.auto_login_worker.isRunning():
                self._add_log("🤖 开始自动填写验证码...")

                # 如果自动登录线程正在等待验证码，立即中断等待并填写
                if hasattr(self.auto_login_worker, 'waiting_for_verification_code') and self.auto_login_worker.waiting_for_verification_code:
                    self._add_log("⚡ 中断验证码等待，立即填写...")
                    self.auto_login_worker.waiting_for_verification_code = False

                # 通过信号通知自动登录线程填写验证码
                self.auto_login_worker._try_fill_verification_code()
            else:
                self._add_log("⚠️ 自动登录进程未运行，无法自动填写")
        except Exception as e:
            self._add_log(f"❌ 触发自动填写验证码失败: {str(e)}")

    def _copy_verification_code(self):
        """复制验证码到剪贴板"""
        if hasattr(self, 'current_code') and self.current_code:
            try:
                pyperclip.copy(self.current_code)
                self._add_log(f"✅ 验证码已复制到剪贴板: {self.current_code}")

                # 临时改变按钮文本
                original_text = self.copy_btn.text()
                self.copy_btn.setText("✅ 已复制!")
                QTimer.singleShot(2000, lambda: self.copy_btn.setText(original_text))

            except Exception as e:
                self._add_log(f"❌ 复制失败: {str(e)}")
        else:
            self._add_log("❌ 没有可复制的验证码")

    def _add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def _update_status(self, status):
        """更新状态"""
        self.status_label.setText(f"状态: {status}")

        # 如果登录完成，重新启用自动登录按钮
        if "登录成功" in status or "失败" in status:
            self.auto_login_btn.setEnabled(True)
            self.auto_login_btn.setText("自动登录")

    def _handle_error(self, error):
        """处理错误"""
        self._add_log(f"❌ {error}")
        self._update_status("发生错误")

    def closeEvent(self, event):
        """关闭事件"""
        if self.monitor_worker:
            self._stop_monitoring()

        if self.auto_login_worker:
            self.auto_login_worker.stop()
            self.auto_login_worker.wait()
            self.auto_login_worker = None

        event.accept()