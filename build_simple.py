#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AugmentCode工具 简化打包脚本
修复了原有打包脚本的问题
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
import time
import platform

# 项目配置
VERSION = "1.0.6"
PROJECT_NAME = "AugmentCode工具"
AUTHOR = "BasicProtein"

def print_step(message):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"🔧 {message}")
    print(f"{'='*60}")

def print_success(message):
    """打印成功信息"""
    print(f"✅ {message}")

def print_error(message):
    """打印错误信息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息"""
    print(f"ℹ️  {message}")

def run_command(cmd, cwd=None, timeout=300):
    """执行命令"""
    print_info(f"执行命令: {cmd}")
    try:
        result = subprocess.run(
            cmd, shell=True, cwd=cwd, capture_output=True,
            text=True, timeout=timeout, encoding='utf-8', errors='replace'
        )
        
        if result.stdout and result.stdout.strip():
            print(f"输出: {result.stdout.strip()}")
        
        if result.stderr and result.stderr.strip():
            print(f"错误: {result.stderr.strip()}")
        
        if result.returncode != 0:
            print_error(f"命令执行失败 (返回码: {result.returncode})")
            return False
        
        print_success("命令执行成功")
        return True
        
    except subprocess.TimeoutExpired:
        print_error(f"命令超时 ({timeout}秒)")
        return False
    except Exception as e:
        print_error(f"命令执行异常: {e}")
        return False

def validate_environment():
    """验证构建环境"""
    print_step("验证构建环境")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 7):
        print_error(f"需要Python 3.7+，当前版本: {python_version}")
        return False
    print_success(f"Python版本: {platform.python_version()}")
    
    # 检查必需文件
    required_files = ['main.py', 'setup.py', 'requirements.txt']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print_success(f"找到文件: {file}")
    
    if missing_files:
        print_error(f"缺少必需文件: {missing_files}")
        return False
    
    # 检查必需目录
    required_dirs = ['augment_tools_core', 'gui_qt6']
    missing_dirs = []
    
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
        else:
            print_success(f"找到目录: {dir_name}")
    
    if missing_dirs:
        print_error(f"缺少必需目录: {missing_dirs}")
        return False
    
    print_success("环境验证通过")
    return True

def clean_build_artifacts():
    """清理构建产物"""
    print_step("清理构建产物")
    
    cleanup_targets = ['build', 'dist', '*.egg-info', '__pycache__']
    cleaned_count = 0
    
    for target in cleanup_targets:
        if '*' in target:
            # 处理通配符
            import glob
            for path in glob.glob(target, recursive=True):
                try:
                    path_obj = Path(path)
                    if path_obj.is_dir():
                        shutil.rmtree(path_obj)
                    else:
                        path_obj.unlink()
                    print_success(f"删除: {path}")
                    cleaned_count += 1
                except Exception as e:
                    print_error(f"删除失败 {path}: {e}")
        else:
            # 处理目录
            target_path = Path(target)
            if target_path.exists():
                try:
                    if target_path.is_dir():
                        shutil.rmtree(target_path)
                    else:
                        target_path.unlink()
                    print_success(f"删除目录: {target}")
                    cleaned_count += 1
                except Exception as e:
                    print_error(f"删除失败 {target}: {e}")
    
    print_success(f"清理完成，删除了 {cleaned_count} 个项目")
    return True

def install_dependencies():
    """安装构建依赖"""
    print_step("安装构建依赖")
    
    # 升级pip
    if not run_command("python -m pip install --upgrade pip"):
        return False
    
    # 安装构建依赖
    dependencies = [
        "build>=0.10.0",
        "wheel>=0.40.0", 
        "setuptools>=68.0.0",
        "pyinstaller>=5.13.0"
    ]
    
    for dep in dependencies:
        print_info(f"安装: {dep}")
        if not run_command(f"pip install {dep}"):
            print_error(f"安装失败: {dep}")
            return False
        print_success(f"安装成功: {dep}")
    
    return True

def build_python_packages():
    """构建Python包"""
    print_step("构建Python包")

    # 创建dist目录
    Path('dist').mkdir(exist_ok=True)

    # 使用setuptools直接构建
    if not run_command("python setup.py sdist bdist_wheel"):
        return False
    
    # 验证构建结果
    wheel_files = list(Path('dist').glob("*.whl"))
    sdist_files = list(Path('dist').glob("*.tar.gz"))
    
    if not wheel_files:
        print_error("没有生成wheel文件")
        return False
    
    if not sdist_files:
        print_error("没有生成源码包")
        return False
    
    for package_file in wheel_files + sdist_files:
        size = package_file.stat().st_size
        print_success(f"构建完成: {package_file.name} ({size:,} 字节)")
    
    return True

def build_executable():
    """构建可执行文件"""
    print_step("构建Windows可执行文件")
    
    # 创建PyInstaller spec文件
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('augment_tools_core', 'augment_tools_core'),
        ('gui_qt6', 'gui_qt6'),
        ('languages', 'languages'),
        ('config', 'config'),
        ('database', 'database'),
        ('README.md', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'PyQt6', 'PyQt6.QtWidgets', 'PyQt6.QtCore', 'PyQt6.QtGui',
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
        'click', 'colorama', 'pathlib', 'sqlite3', 'json', 'uuid',
        'platform', 'subprocess', 'threading', 'queue', 'time', 'psutil',
        'xml.etree.ElementTree', 'shutil', 'tempfile', 'requests', 'urllib3',
        'gui_qt6.main_window', 'gui_qt6.main_page', 'gui_qt6.login_dialog',
        'gui_qt6.api_service', 'gui_qt6.recipient_selector', 'gui_qt6.version_dialog',
        'config_manager', 'language_manager', 'welcome_dialog'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=['matplotlib', 'numpy', 'pandas', 'scipy', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='{PROJECT_NAME}-v{VERSION}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    spec_filename = f'{PROJECT_NAME}-v{VERSION}.spec'
    with open(spec_filename, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print_success(f"创建spec文件: {spec_filename}")
    
    # 运行PyInstaller
    if not run_command(f"pyinstaller {spec_filename} --clean --noconfirm --distpath dist", timeout=600):
        return False
    
    # 验证可执行文件
    exe_path = Path('dist') / f'{PROJECT_NAME}-v{VERSION}.exe'
    if not exe_path.exists():
        print_error(f"可执行文件未生成: {exe_path}")
        return False
    
    size = exe_path.stat().st_size
    print_success(f"可执行文件构建完成: {exe_path.name} ({size:,} 字节, {size/1024/1024:.2f} MB)")
    
    # 清理spec文件
    Path(spec_filename).unlink()
    
    return True

def create_portable_package():
    """创建便携包"""
    print_step("创建便携包")
    
    portable_dir = Path(f"{PROJECT_NAME}-v{VERSION}-Portable")
    
    try:
        # 创建便携目录
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        portable_dir.mkdir()
        
        # 复制核心文件
        core_files = ['main.py', 'gui.py', 'setup.py', 'requirements.txt', 
                     'README.md', 'config_manager.py', 'language_manager.py', 
                     'welcome_dialog.py']
        
        for file in core_files:
            src = Path(file)
            if src.exists():
                shutil.copy2(src, portable_dir)
                print_success(f"复制文件: {file}")
        
        # 复制目录
        dirs_to_copy = ['augment_tools_core', 'gui_qt6', 'languages', 'config', 'database', 'utils']
        
        for dir_name in dirs_to_copy:
            src_dir = Path(dir_name)
            if src_dir.exists():
                shutil.copytree(src_dir, portable_dir / dir_name,
                               ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                print_success(f"复制目录: {dir_name}/")
        
        # 创建启动脚本
        create_startup_scripts(portable_dir)
        
        # 创建ZIP包
        zip_path = Path('dist') / f"{portable_dir.name}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
            for root, dirs, files in os.walk(portable_dir):
                for file in files:
                    file_path = Path(root) / file
                    arcname = file_path.relative_to(Path('.'))
                    zipf.write(file_path, arcname)
        
        # 清理临时目录
        shutil.rmtree(portable_dir)
        
        size = zip_path.stat().st_size
        print_success(f"便携包创建完成: {zip_path.name} ({size:,} 字节)")
        return True
        
    except Exception as e:
        print_error(f"便携包创建失败: {e}")
        return False

def create_startup_scripts(portable_dir):
    """创建启动脚本"""
    # Windows批处理脚本
    win_script = f'''@echo off
chcp 65001 >nul
title {PROJECT_NAME} v{VERSION}
echo.
echo ========================================
echo   {PROJECT_NAME} v{VERSION}
echo   多IDE维护工具包
echo   Author: {AUTHOR}
echo ========================================
echo.
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: 未找到Python!
    echo 请从 https://python.org 安装Python 3.7+
    pause
    exit /b 1
)
echo 启动GUI界面...
python main.py
pause
'''
    
    with open(portable_dir / f'启动-{PROJECT_NAME}.bat', 'w', encoding='utf-8') as f:
        f.write(win_script)
    
    print_success("创建Windows启动脚本")

def main():
    """主函数"""
    start_time = time.time()
    
    print(f"\n{'='*80}")
    print(f"{PROJECT_NAME} v{VERSION} 简化构建系统")
    print(f"多IDE维护工具包")
    print(f"作者: {AUTHOR}")
    print(f"{'='*80}\n")
    
    # 构建步骤
    build_steps = [
        ("环境验证", validate_environment),
        ("清理构建产物", clean_build_artifacts),
        ("安装依赖", install_dependencies),
        ("构建Python包", build_python_packages),
        ("构建可执行文件", build_executable),
        ("创建便携包", create_portable_package),
    ]
    
    failed_steps = []
    
    # 执行构建步骤
    for step_name, step_function in build_steps:
        try:
            if not step_function():
                print_error(f"构建步骤失败: {step_name}")
                failed_steps.append(step_name)
                
                # 关键步骤失败则停止
                if step_name in ["环境验证", "安装依赖"]:
                    print_error("关键步骤失败，停止构建")
                    break
        except KeyboardInterrupt:
            print_error("用户中断构建")
            return 1
        except Exception as e:
            print_error(f"构建步骤异常 {step_name}: {e}")
            failed_steps.append(step_name)
    
    # 构建报告
    build_time = time.time() - start_time
    
    print_step("构建完成")
    
    if failed_steps:
        print_error(f"构建完成但有错误。失败步骤: {', '.join(failed_steps)}")
    else:
        print_success("所有构建步骤成功完成！")
    
    print_info(f"总构建时间: {build_time:.2f} 秒")
    
    # 列出生成的文件
    dist_dir = Path('dist')
    if dist_dir.exists():
        artifacts = list(dist_dir.iterdir())
        if artifacts:
            print_info("生成的文件:")
            for artifact in sorted(artifacts):
                if artifact.is_file():
                    size = artifact.stat().st_size
                    print_info(f"  📦 {artifact.name} ({size:,} 字节)")
    
    return 0 if not failed_steps else 1

if __name__ == "__main__":
    sys.exit(main())
