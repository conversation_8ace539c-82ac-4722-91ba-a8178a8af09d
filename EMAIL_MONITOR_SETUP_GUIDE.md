# QQ邮箱验证码监控功能配置指南

## 功能概述

新增的邮箱监控功能可以自动监控QQ邮箱中的验证码邮件，实时显示验证码信息。该功能通过IMAP协议连接QQ邮箱，监控新邮件并识别包含验证码的邮件。

## 配置步骤

### 1. 开启QQ邮箱IMAP服务

1. **登录QQ邮箱**
   - 访问 https://mail.qq.com
   - 使用您的QQ账号登录

2. **开启IMAP服务**
   - 点击邮箱设置 → 账户
   - 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
   - 开启"IMAP/SMTP服务"
   - 按照提示发送短信验证

3. **获取授权码**
   - 开启服务后，系统会生成一个授权码
   - **重要：这个授权码就是您的邮箱密码，请妥善保存**

### 2. 配置代码中的邮箱信息

在 `gui_qt6/email_dialog.py` 文件中找到 `EMAIL_CONFIG` 配置：

```python
# 邮箱配置 - 在代码中配置，不暴露在界面上
EMAIL_CONFIG = {
    'imap_server': 'imap.qq.com',
    'imap_port': 993,
    'email': '<EMAIL>',  # 请替换为实际的QQ邮箱
    'password': 'your_app_password',   # 请替换为QQ邮箱的授权码
    'check_interval': 10,  # 检查间隔（秒）
    'keywords': ['验证码', '验证', 'verification', 'code']  # 验证码关键词
}
```

**需要修改的配置项：**

- `email`: 替换为您的QQ邮箱地址，例如：`'<EMAIL>'`
- `password`: 替换为步骤1中获取的授权码，例如：`'abcdefghijklmnop'`

**可选配置项：**

- `check_interval`: 邮件检查间隔（秒），默认10秒
- `keywords`: 验证码关键词列表，用于识别验证码邮件

### 3. 配置示例

```python
EMAIL_CONFIG = {
    'imap_server': 'imap.qq.com',
    'imap_port': 993,
    'email': '<EMAIL>',        # 您的QQ邮箱
    'password': 'abcdefghijklmnop',      # QQ邮箱授权码
    'check_interval': 10,
    'keywords': ['验证码', '验证', 'verification', 'code']
}
```

## 使用方法

### 1. 打开邮箱监控

1. 启动AugmentCode工具
2. 点击右上角的"邮箱"按钮
3. 在弹出的对话框中点击"开始监控"

### 2. 监控界面说明

- **开始监控按钮**: 启动邮箱监控服务
- **停止监控按钮**: 停止邮箱监控服务
- **状态显示**: 显示当前连接状态
- **监控日志**: 实时显示监控日志和收到的验证码邮件

### 3. 验证码邮件识别

系统会自动识别包含以下关键词的邮件：
- 验证码
- 验证
- verification
- code

当收到验证码邮件时，会在日志中显示：
- 📧 发件人信息
- 📧 邮件主题
- 📧 邮件内容（前200字符）
- 📧 接收时间

## 安全注意事项

### 1. 授权码安全

- **授权码等同于邮箱密码**，请妥善保管
- 不要将授权码分享给他人
- 如果授权码泄露，请立即到QQ邮箱重新生成

### 2. 网络安全

- 程序使用SSL加密连接（IMAP over SSL）
- 端口993是QQ邮箱的安全IMAP端口
- 所有通信都经过加密传输

### 3. 隐私保护

- 邮箱配置信息仅存储在本地代码中
- 不会上传到任何服务器
- 监控日志仅在本地显示

## 故障排除

### 1. 连接失败

**错误信息**: "邮箱连接失败"

**可能原因和解决方案**:
- 检查邮箱地址是否正确
- 检查授权码是否正确
- 确认已开启QQ邮箱IMAP服务
- 检查网络连接是否正常

### 2. 授权失败

**错误信息**: "登录失败" 或 "认证错误"

**解决方案**:
- 重新获取QQ邮箱授权码
- 确认使用的是授权码而不是QQ密码
- 检查邮箱地址格式是否正确

### 3. 无法收到验证码

**可能原因**:
- 验证码邮件可能不包含预设的关键词
- 邮件可能被分类到其他文件夹

**解决方案**:
- 在代码中添加更多关键词到 `keywords` 列表
- 检查QQ邮箱的垃圾邮件文件夹

### 4. 监控中断

**可能原因**:
- 网络连接不稳定
- QQ邮箱服务器临时不可用

**解决方案**:
- 程序会自动重试连接
- 如果持续失败，请重新启动监控

## 技术实现

### 1. 核心技术

- **IMAP协议**: 用于连接和读取邮箱
- **SSL加密**: 确保连接安全
- **多线程**: 后台监控不阻塞界面
- **PyQt6信号**: 实现线程间通信

### 2. 监控流程

1. 连接到QQ邮箱IMAP服务器
2. 登录并选择收件箱
3. 获取当前邮件数量作为基准
4. 定期检查是否有新邮件
5. 分析新邮件内容，识别验证码邮件
6. 在界面中显示验证码信息

### 3. 邮件解析

- 解码邮件头信息（主题、发件人）
- 提取邮件正文内容
- 支持多部分邮件格式
- 处理各种字符编码

## 扩展功能

### 1. 自定义关键词

可以在 `EMAIL_CONFIG` 中添加更多关键词：

```python
'keywords': [
    '验证码', '验证', 'verification', 'code',
    '动态码', '安全码', 'OTP', '一次性密码'
]
```

### 2. 调整检查间隔

根据需要调整邮件检查频率：

```python
'check_interval': 5,  # 5秒检查一次（更频繁）
'check_interval': 30, # 30秒检查一次（较少频繁）
```

### 3. 支持其他邮箱

理论上可以支持其他支持IMAP的邮箱服务，只需修改：
- `imap_server`: 邮箱服务器地址
- `imap_port`: IMAP端口号
- 相应的认证信息

## 总结

QQ邮箱验证码监控功能为用户提供了便捷的验证码获取方式，通过自动监控邮箱，实时显示验证码信息，提高了工作效率。配置简单，使用方便，是一个实用的辅助工具。

**重要提醒**: 请确保在使用前正确配置邮箱信息，并妥善保管授权码，确保账户安全。
