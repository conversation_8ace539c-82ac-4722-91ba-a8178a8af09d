<?php


include('confing/common.php');
$ckxz = $DB->get_row("select settings,api_ck,api_xd,api_proportion from qingka_wangke_config");
$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;
@header('Content-Type: application/json; charset=UTF-8');


	switch ($act) {
		case 'getmoney'://查询当前余额
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			if ($uid == '' || $key == '') {
				exit('{"code":0,"msg":"所有项目不能为空"}');
			}
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				$result = array(
					'code' => 1,
					'msg' => '查询成功',
					'money' => $row['money']
				);
				exit(json_encode($result));
			}
			break;
		// 版本更新后台
		case 'getupdateinfo':
			$uid = trim(strip_tags(addslashes($_POST['uid'])));
			$key = trim(strip_tags(addslashes($_POST['key'])));

			if ($uid == '' || $key == '') {
				exit('{"code":0,"msg":"小本生意，请根据要求来可以么"}');
			}

			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");

			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				$new = isset($conf['banbennr']) ? $conf['banbennr'] : '默认内容';
				$version = isset($conf['banben']) ? $conf['banben'] : '当前版本';
				$url = isset($conf['banbenurl']) ? $conf['banbenurl'] : '安装包地址';


				$latestVersion = [
					"code" => "1",
					"version" => $version,
					"url" => "$url",
					"new" => $new
				];

				$result = array(
					'code' => 1,
					'msg' => '查询成功',
					'data' => $latestVersion
				);
				exit(json_encode($result));
			}
			break;
		// 版本更新
		case 'updateversion':
			$uid = trim(strip_tags(addslashes($_POST['uid'])));
			$key = trim(strip_tags(addslashes($_POST['key'])));
			$newVersion = trim(strip_tags(addslashes($_POST['newVersion'])));

			if ($uid == '' || $key == '' || $newVersion == '') {
				exit('{"code":0,"msg":"参数错误，请根据要求来可以么"}');
			}

			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");

			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				// 查询当前的k值
				$configRow = $DB->get_row("SELECT k FROM qingka_wangke_config WHERE v='banben'");

				if (!$configRow) {
					exit('{"code":-3,"msg":"配置信息未找到"}');
				}

				// 更新数据库中的版本信息
				$updateResult = $DB->query("UPDATE `qingka_wangke_config` SET k='{$newVersion}' WHERE v='banben'");

				if ($updateResult) {
					$result = array("code" => 1, "msg" => "版本更新成功");
				} else {
					$result = array("code" => -3, "msg" => "版本更新失败");
				}

				exit(json_encode($result));
			}
			break;



	
		case 'uporder': //进度刷新
			$oid = trim(strip_tags(daddslashes($_POST['oid'])));
			$row = $DB->get_row("select * from qingka_wangke_order where oid='$oid'");
			if ($row['dockstatus'] == '99') {
				$result = pre_zy($oid);
				exit(json_encode($result));
			}
			$result = processCx($oid);
			for ($i = 0; $i < count($result); $i++) {
				$DB->query("update qingka_wangke_order set `yid`='{$result[$i]['yid']}',`status`='{$result[$i]['status_text']}',`process`='{$result[$i]['process']}',`remarks`='{$result[$i]['remarks']}' where `user`='{$result[$i]['user']}' and `kcname`='{$result[$i]['kcname']}' and `oid`='{$oid}'");
			}
			$upmsg = "同步成功";
			jsonReturn(1, $upmsg);
			break;
		case 'getadd'://查询判断下单
			$uid = daddslashes($_POST['uid']);
			$key = daddslashes($_POST['key']);
			$platform = daddslashes($_POST['platform']);
			$school = daddslashes($_POST['school']);
			$user = daddslashes($_POST['user']);
			$pass = daddslashes($_POST['pass']);
			$kcname = daddslashes($_POST['kcname']);
			$miaoshua = 0;
			$clientip = real_ip();
			if ($uid == '' || $key == '' || $platform == '' || $school == '' || $user == '' || $pass == '' || $kcname == '') {
				exit('{"code":0,"msg":"所有项目不能为空"}');
			}
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if ($row['key'] == '0') {
				exit('{"code":-1,"msg":"你还没有开通接口哦"}');
			}
			if ($row['key'] != $key) {
				exit('{"code":-2,"msg":"密匙错误"}');
			} else {
				$rs = $DB->get_row("select * from qingka_wangke_class where cid='$platform' limit 1 ");
				//$danjia=$rs['price']*$row['addprice'];

				if ($rs['yunsuan'] == "*") {
					$danjia = round($rs['price'] * $row['addprice'], 2);
				} elseif ($rs['yunsuan'] == "+") {
					$danjia = round($rs['price'] + $row['addprice'], 2);
				} else {
					$danjia = round($rs['price'] * $row['addprice'], 2);
				}

				if ($danjia == 0 || $row['addprice'] < 0.1) {
					exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
				}
				if ($row['money'] < $danjia) {
					exit('{"code":-1,"msg":"余额不足"}');
				}
				$a = getWk($rs['queryplat'], $rs['getnoun'], $school, $user, $pass, $rs['name']);

				if ($a['code'] == '1') {
					for ($i = 0; $i < count($a['data']); $i++) {
						$kcid1 = $a['data'][$i]['id'];
						$kcname1 = $a['data'][$i]['name'];
						similar_text($kcname1, $kcname, $percent);
						if ($percent > "90%") {
							if ($rs['yunsuan'] == "*") {
								$danjia = round($rs['price'] * $row['addprice'], 2);
							} elseif ($rs['yunsuan'] == "+") {
								$danjia = round($rs['price'] + $row['addprice'], 2);
							} else {
								$danjia = round($rs['price'] * $row['addprice'], 2);
							}
							if ($rs['docking'] == '0') {
								$dockstatus = '99';
							} else {
								$dockstatus = '0';
							}
							$DB->query("insert into qingka_wangke_order (uid,cid,hid,ptname,school,user,pass,kcid,kcname,fees,noun,miaoshua,addtime,ip,dockstatus) values ('{$uid}','{$rs['cid']}','{$rs['docking']}','{$rs['name']}','{$school}','$user','$pass','$kcid1','$kcname1','{$danjia}','{$rs['noun']}','$miaoshua','$date','$clientip','$dockstatus') ");//将对应课程写入数据库	               	           	              	           	               
							$DB->query("update qingka_wangke_user set money=money-'{$danjia}' where uid='$uid' limit 1 ");
							wlog($row['uid'], "API添加任务", "{$user} {$pass} {$kcname} 扣除{$danjia}元！", -$danjia);
							$ok = 1;
							break;
						}

					}
					if ($ok == 1) {
						exit('{"code":0,"msg":"提交成功","status":0,"message":"提交成功","id":"订单号登录后台自行查看，老子懒得写了"}');
					} else {
						exit('{"code":-1,"msg":"请完整输入课程名字","status":-1,"message":"请完整输入课程名字"}');
					}

				} else {
					$result = array("code" => -1, 'msg' => $a[0]['msg']);
					exit(json_encode($result));
				}


			}
			break;

	
	}
}
?>